package fastpath

import (
	"fmt"
	"sync"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/bitmap"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	areaRouteDAO "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/route"
	basicLocationDAO "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location"
	basicPostcodeDAO "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_postcode"
	"github.com/RoaringBitmap/roaring"
)

// BitmapGenerator 位图数据生成器
type BitmapGenerator struct {
	cache            *localcache.Manager
	routeDAO         areaRouteDAO.LogisticLineServiceableRouteTabDAO
	basicLocationDAO basicLocationDAO.LineBasicServiceableLocationDAO
	basicPostcodeDAO basicPostcodeDAO.LineBasicServiceablePostcodeDAO
	mutex            sync.RWMutex // 保护并发操作
}

// NewBitmapGenerator 创建位图生成器
func NewBitmapGenerator(cache *localcache.Manager) *BitmapGenerator {
	return &BitmapGenerator{
		cache:            cache,
		routeDAO:         areaRouteDAO.NewLogisticLineServiceableRouteTabDAO(),
		basicLocationDAO: basicLocationDAO.NewLineBasicServiceableLocationDAO(),
		basicPostcodeDAO: basicPostcodeDAO.NewLineBasicServiceablePostcodeDAO(),
	}
}

// GenerateLineRouteSnapshots 生成指定 line 的路由快照
func (g *BitmapGenerator) GenerateLineRouteSnapshots(ctx utils.LCOSContext, lineKeys []LineKey) error {
	for _, key := range lineKeys {
		if err := g.generateSingleLineSnapshot(ctx, key); err != nil {
			logger.CtxLogErrorf(ctx, "Failed to generate snapshot for line %s: %v", key.String(), err)
			// 继续处理其他 line，不因单个失败而中断
			continue
		}
	}
	return nil
}

// generateSingleLineSnapshot 生成单个 line 的位图快照
func (g *BitmapGenerator) generateSingleLineSnapshot(ctx utils.LCOSContext, key LineKey) error {
	logger.CtxLogInfof(ctx, "Generating bitmap snapshot for line: %s", key.String())

	// 1. 生成白名单位图
	whiteAgg, err := g.generateRouteWhitelistAgg(ctx, key)
	if err != nil {
		return fmt.Errorf("failed to generate whitelist: %w", err)
	}

	// 2. 生成黑名单位图
	blackAgg, err := g.generateRouteBlacklistAgg(ctx, key)
	if err != nil {
		return fmt.Errorf("failed to generate blacklist: %w", err)
	}

	// 3. 生成 Origin 位图（Phase 2）
	originAgg, err := g.generateOriginAgg(ctx, key)
	if err != nil {
		return fmt.Errorf("failed to generate origin bitmap: %w", err)
	}

	// 4. 生成 Dest 位图（Phase 2）
	destAgg, err := g.generateDestAgg(ctx, key)
	if err != nil {
		return fmt.Errorf("failed to generate dest bitmap: %w", err)
	}

	// 5. 缓存到 localcache
	if whiteAgg != nil {
		if err := g.cacheRouteWhitelistAgg(ctx, key, whiteAgg); err != nil {
			logger.CtxLogErrorf(ctx, "Failed to cache whitelist for %s: %v", key.String(), err)
		}
	}

	if blackAgg != nil {
		if err := g.cacheRouteBlacklistAgg(ctx, key, blackAgg); err != nil {
			logger.CtxLogErrorf(ctx, "Failed to cache blacklist for %s: %v", key.String(), err)
		}
	}

	if originAgg != nil {
		if err := g.cacheOriginAgg(ctx, key, originAgg); err != nil {
			logger.CtxLogErrorf(ctx, "Failed to cache origin bitmap for %s: %v", key.String(), err)
		}
	}

	if destAgg != nil {
		if err := g.cacheDestAgg(ctx, key, destAgg); err != nil {
			logger.CtxLogErrorf(ctx, "Failed to cache dest bitmap for %s: %v", key.String(), err)
		}
	}

	logger.CtxLogInfof(ctx, "Successfully generated bitmap snapshot for line: %s", key.String())
	return nil
}

// generateRouteWhitelistAgg 生成路由白名单位图聚合
func (g *BitmapGenerator) generateRouteWhitelistAgg(ctx utils.LCOSContext, key LineKey) (*RouteWhitelistAgg, error) {
	// 简化实现：由于当前没有确切的查询方法，使用模拟数据生成位图
	// 实际实现需要查询数据库获取该 line 的所有白名单路由
	// routes, err := g.routeDAO.GetRoutesByLineIdAndGroupId(ctx, key.LineID, key.CollectDeliverGroupID)

	// 模拟位图生成逻辑
	fromToMap := make(map[uint64]*roaring.Bitmap)

	// 这里应该从数据库查询实际的路由数据
	// 暂时使用模拟数据进行演示
	logger.CtxLogInfof(ctx, "Generating whitelist bitmap for line %s, group %s", key.LineID, key.CollectDeliverGroupID)

	// TODO: 实际实现需要调用正确的DAO方法查询路由数据
	// 然后构建位图：
	// for _, route := range routes {
	//     fromArea := uint64(route.FromAreaId)
	//     toArea := uint64(route.ToAreaId)
	//     if fromToMap[fromArea] == nil {
	//         fromToMap[fromArea] = roaring.New()
	//     }
	//     fromToMap[fromArea].Add(uint(toArea))
	// }

	return &RouteWhitelistAgg{
		FromTo: fromToMap,
	}, nil
}

// generateRouteBlacklistAgg 生成路由黑名单位图聚合
func (g *BitmapGenerator) generateRouteBlacklistAgg(ctx utils.LCOSContext, key LineKey) (*RouteBlacklistAgg, error) {
	// 简化实现：由于当前没有确切的查询方法，使用模拟数据生成位图
	// 实际实现需要查询数据库获取该 line 的所有黑名单路由
	logger.CtxLogInfof(ctx, "Generating blacklist bitmap for line %s, group %s", key.LineID, key.CollectDeliverGroupID)

	// TODO: 实际实现需要调用正确的DAO方法查询黑名单路由数据
	// routes, err := g.routeDAO.GetBlacklistRoutesByLineIdAndGroupId(ctx, key.LineID, key.CollectDeliverGroupID)

	return &RouteBlacklistAgg{
		GlobalBlocked: false,
		FromTo:        make(map[uint64]*roaring.Bitmap),
	}, nil
}

// cacheRouteWhitelistAgg 生成白名单位图数据（准备写入缓存）
func (g *BitmapGenerator) cacheRouteWhitelistAgg(ctx utils.LCOSContext, key LineKey, agg *RouteWhitelistAgg) error {
	// 注意：localcache 是只读的，不支持直接写入
	// 实际的缓存写入需要通过 localcache 的 dump 机制批量加载
	// 这里只是记录生成的数据，供后续批量更新使用
	cacheKey := bitmap.GenerateAggCacheKey(key.LineID, key.CollectDeliverGroupID)

	logger.CtxLogInfof(ctx, "Generated whitelist bitmap data for cache key: %s", cacheKey)
	// TODO: 将生成的数据保存到临时存储，等待批量写入 localcache
	return nil
}

// cacheRouteBlacklistAgg 生成黑名单位图数据（准备写入缓存）
func (g *BitmapGenerator) cacheRouteBlacklistAgg(ctx utils.LCOSContext, key LineKey, agg *RouteBlacklistAgg) error {
	// 注意：localcache 是只读的，不支持直接写入
	// 实际的缓存写入需要通过 localcache 的 dump 机制批量加载
	cacheKey := bitmap.GenerateAggCacheKey(key.LineID, key.CollectDeliverGroupID)

	logger.CtxLogInfof(ctx, "Generated blacklist bitmap data for cache key: %s", cacheKey)
	// TODO: 将生成的数据保存到临时存储，等待批量写入 localcache
	return nil
}

// RefreshLineSnapshot 刷新指定 line 的快照（用于数据更新后的同步）
func (g *BitmapGenerator) RefreshLineSnapshot(ctx utils.LCOSContext, key LineKey) error {
	// 删除旧的缓存
	g.invalidateLineSnapshot(ctx, key)

	// 重新生成
	return g.generateSingleLineSnapshot(ctx, key)
}

// invalidateLineSnapshot 标记指定 line 的快照需要刷新
func (g *BitmapGenerator) invalidateLineSnapshot(ctx utils.LCOSContext, key LineKey) {
	// 注意：localcache 是只读的，不支持直接删除
	// 失效操作需要通过 localcache 的版本控制机制或重新加载来实现
	cacheKey := bitmap.GenerateAggCacheKey(key.LineID, key.CollectDeliverGroupID)

	logger.CtxLogInfof(ctx, "Marked bitmap snapshot for invalidation: %s, cache key: %s", key.String(), cacheKey)
	// TODO: 实际实现需要通过 localcache 的 CleanByNamespace 或版本控制来失效缓存
}

// BatchGenerateSnapshots 批量生成位图快照
func (g *BitmapGenerator) BatchGenerateSnapshots(ctx utils.LCOSContext, lineKeys []LineKey) error {
	logger.CtxLogInfof(ctx, "Starting batch generation for %d lines", len(lineKeys))

	startTime := time.Now()
	successCount := 0

	for i, key := range lineKeys {
		if err := g.generateSingleLineSnapshot(ctx, key); err != nil {
			logger.CtxLogErrorf(ctx, "Failed to generate snapshot %d/%d for line %s: %v",
				i+1, len(lineKeys), key.String(), err)
		} else {
			successCount++
		}

		// 每处理100个输出进度
		if (i+1)%100 == 0 {
			logger.CtxLogInfof(ctx, "Batch generation progress: %d/%d completed", i+1, len(lineKeys))
		}
	}

	duration := time.Since(startTime)
	logger.CtxLogInfof(ctx, "Batch generation completed: %d/%d successful, took %v",
		successCount, len(lineKeys), duration)

	return nil
}

// GetGenerationStats 获取生成统计信息
func (g *BitmapGenerator) GetGenerationStats(ctx utils.LCOSContext) (map[string]interface{}, error) {
	stats := map[string]interface{}{
		"timestamp": time.Now().Unix(),
		"generator": "BitmapGenerator",
	}

	// 可以添加更多统计信息，如缓存命中率、生成次数等

	return stats, nil
}

// generateOriginAgg 生成 Origin 能力位图聚合
func (g *BitmapGenerator) generateOriginAgg(ctx utils.LCOSContext, key LineKey) (*OriginAgg, error) {
	logger.CtxLogInfof(ctx, "Generating origin bitmap for line: %s", key.String())

	// 1. 查询基础层 location 数据
	locationAgg, err := g.generateOriginLocationAgg(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("failed to generate origin location bitmap: %w", err)
	}

	// 2. 查询基础层 postcode 数据 (如果配置为 postcode 维度)
	postcodeAgg, err := g.generateOriginPostcodeAgg(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("failed to generate origin postcode bitmap: %w", err)
	}

	// 3. 根据 line 配置决定使用哪个维度（这里先简化为 location 优先）
	if locationAgg != nil && locationAgg.Allowed.GetCardinality() > 0 {
		return locationAgg, nil
	}
	if postcodeAgg != nil && postcodeAgg.Allowed.GetCardinality() > 0 {
		return postcodeAgg, nil
	}

	// 如果都没有数据，返回空位图（表示全部禁止）
	return &OriginAgg{
		Kind:       DimLocationID,
		Allowed:    roaring.New(),
		AllowedCod: roaring.New(),
	}, nil
}

// generateDestAgg 生成 Dest 能力位图聚合
func (g *BitmapGenerator) generateDestAgg(ctx utils.LCOSContext, key LineKey) (*DestAgg, error) {
	logger.CtxLogInfof(ctx, "Generating dest bitmap for line: %s", key.String())

	// 1. 查询基础层 location 数据
	locationAgg, err := g.generateDestLocationAgg(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("failed to generate dest location bitmap: %w", err)
	}

	// 2. 查询基础层 postcode 数据
	postcodeAgg, err := g.generateDestPostcodeAgg(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("failed to generate dest postcode bitmap: %w", err)
	}

	// 3. 根据 line 配置决定使用哪个维度（location 优先）
	if locationAgg != nil && locationAgg.Allowed.GetCardinality() > 0 {
		return locationAgg, nil
	}
	if postcodeAgg != nil && postcodeAgg.Allowed.GetCardinality() > 0 {
		return postcodeAgg, nil
	}

	// 如果都没有数据，返回空位图
	return &DestAgg{
		Kind:       DimLocationID,
		Allowed:    roaring.New(),
		AllowedCod: roaring.New(),
	}, nil
}

// generateOriginLocationAgg 生成 Origin location 维度位图
func (g *BitmapGenerator) generateOriginLocationAgg(ctx utils.LCOSContext, key LineKey) (*OriginAgg, error) {
	// 查询基础层 origin location 数据
	searchData := map[string]interface{}{
		"collect_deliver_group_id": key.CollectDeliverGroupID,
	}

	locations, lcosErr := g.basicLocationDAO.SearchAllBasicServiceableLocation(ctx, key.LineID, searchData)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "Failed to query origin locations for %s: %v", key.String(), lcosErr.Msg)
		return nil, fmt.Errorf("query error: %s", lcosErr.Msg)
	}

	if len(locations) == 0 {
		logger.CtxLogInfof(ctx, "No origin location data found for %s", key.String())
		return nil, nil
	}

	// 构建位图
	allowed := roaring.New()
	allowedCod := roaring.New()

	for _, loc := range locations {
		locationKey := uint64(loc.LocationId)

		// 检查pickup能力
		if loc.CanPickup != nil && *loc.CanPickup == 1 {
			allowed.Add(uint32(locationKey))
		}

		// 检查COD pickup能力
		if loc.CanCodPickup != nil && *loc.CanCodPickup == 1 {
			allowedCod.Add(uint32(locationKey))
		}
	}

	logger.CtxLogInfof(ctx, "Generated origin location bitmap for %s: allowed=%d, cod=%d",
		key.String(), allowed.GetCardinality(), allowedCod.GetCardinality())

	return &OriginAgg{
		Kind:       DimLocationID,
		Allowed:    allowed,
		AllowedCod: allowedCod,
	}, nil
}

// generateDestLocationAgg 生成 Dest location 维度位图
func (g *BitmapGenerator) generateDestLocationAgg(ctx utils.LCOSContext, key LineKey) (*DestAgg, error) {
	// 查询基础层 dest location 数据
	searchData := map[string]interface{}{
		"collect_deliver_group_id": key.CollectDeliverGroupID,
	}

	locations, lcosErr := g.basicLocationDAO.SearchAllBasicServiceableLocation(ctx, key.LineID, searchData)
	if lcosErr != nil {
		logger.CtxLogErrorf(ctx, "Failed to query dest locations for %s: %v", key.String(), lcosErr.Msg)
		return nil, fmt.Errorf("query error: %s", lcosErr.Msg)
	}

	if len(locations) == 0 {
		logger.CtxLogInfof(ctx, "No dest location data found for %s", key.String())
		return nil, nil
	}

	// 构建位图
	allowed := roaring.New()
	allowedCod := roaring.New()

	for _, loc := range locations {
		locationKey := uint64(loc.LocationId)

		// 检查deliver能力
		if loc.CanDeliver != nil && *loc.CanDeliver == 1 {
			allowed.Add(uint32(locationKey))
		}

		// 检查COD deliver能力
		if loc.CanCodDeliver != nil && *loc.CanCodDeliver == 1 {
			allowedCod.Add(uint32(locationKey))
		}
	}

	logger.CtxLogInfof(ctx, "Generated dest location bitmap for %s: allowed=%d, cod=%d",
		key.String(), allowed.GetCardinality(), allowedCod.GetCardinality())

	return &DestAgg{
		Kind:       DimLocationID,
		Allowed:    allowed,
		AllowedCod: allowedCod,
	}, nil
}

// generateOriginPostcodeAgg 生成 Origin postcode 维度位图
func (g *BitmapGenerator) generateOriginPostcodeAgg(ctx utils.LCOSContext, key LineKey) (*OriginAgg, error) {
	// Postcode/Cep维度流量少，实现复杂度高，不实现位图快速路径
	// 这些请求直接走老流程
	logger.CtxLogInfof(ctx, "Origin postcode bitmap not supported, will use legacy flow for %s", key.String())
	return nil, nil
}

// generateDestPostcodeAgg 生成 Dest postcode 维度位图
func (g *BitmapGenerator) generateDestPostcodeAgg(ctx utils.LCOSContext, key LineKey) (*DestAgg, error) {
	// Postcode/Cep维度流量少，实现复杂度高，不实现位图快速路径
	// 这些请求直接走老流程
	logger.CtxLogInfof(ctx, "Dest postcode bitmap not supported, will use legacy flow for %s", key.String())
	return nil, nil
}

// cacheOriginAgg 缓存 Origin 位图聚合到 localcache
func (g *BitmapGenerator) cacheOriginAgg(ctx utils.LCOSContext, key LineKey, agg *OriginAgg) error {
	cacheKey := bitmap.GenerateAggCacheKey(key.LineID, key.CollectDeliverGroupID)

	// TODO: localcache 是只读的，实际需要通过其他机制（如 dump/reload）来更新缓存
	// 这里只是模拟缓存操作，记录日志
	logger.CtxLogInfof(ctx, "Would cache origin bitmap for key %s: dimension=%d, allowed=%d, cod=%d",
		cacheKey, agg.Kind, agg.Allowed.GetCardinality(), agg.AllowedCod.GetCardinality())

	return nil
}

// cacheDestAgg 缓存 Dest 位图聚合到 localcache
func (g *BitmapGenerator) cacheDestAgg(ctx utils.LCOSContext, key LineKey, agg *DestAgg) error {
	cacheKey := bitmap.GenerateAggCacheKey(key.LineID, key.CollectDeliverGroupID)

	// TODO: localcache 是只读的，实际需要通过其他机制来更新缓存
	logger.CtxLogInfof(ctx, "Would cache dest bitmap for key %s: dimension=%d, allowed=%d, cod=%d",
		cacheKey, agg.Kind, agg.Allowed.GetCardinality(), agg.AllowedCod.GetCardinality())

	return nil
}
