package fastpath

import (
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
	"google.golang.org/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lcos_error"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lfs_service"
)

type Evaluator struct{}

type EvaluateInput struct {
	LaneCode      string
	LaneInfo      *lfs_service.LaneCodeStruct
	ProductReq    *pb.LaneAreaServiceabilityReq
	LineSnapshots map[string]*LineSnapshot

	OriginKey   uint64   // 由 PickupAddr 规范化得到（locationId/postcode/cep）
	DestKey     uint64   // 由 DeliverAddr 规范化得到
	FromAreaIDs []uint64 // 由 PickupAddr → AreaRef 映射获得
	ToAreaIDs   []uint64 // 由 DeliverAddr → AreaRef 映射获得
	RouteMode   uint8    // ROUTE_SUPPORTED / ROUTE_UNSUPPORTED
	IsCOD       bool
}

type EvaluateResult struct {
	Hit         bool
	Serviceable *pb.LaneAreaServiceable
}

// NewEvaluator 创建评估器实例
func NewEvaluator() *Evaluator {
	return &Evaluator{}
}

func (e *Evaluator) EvaluateLaneByBitmap(in EvaluateInput) EvaluateResult {
	// 只支持Location维度，Postcode/Cep流量少且实现复杂，直接走老流程
	if !IsLocationOnlySupported() {
		return EvaluateResult{Hit: false}
	}

	// 检查是否为非Location维度请求，如果是则走老流程
	// 这里需要根据实际的维度判断逻辑来实现
	// 如果请求包含Postcode或CepRange相关信息而非LocationID，则返回未命中
	// TODO: 根据实际的请求结构添加维度检查逻辑

	// 获取 lane 下的所有 lineId
	lineIds := extractLineIDsFromLane(in.LaneInfo)

	// 检查是否所有 line 都有快照
	for _, lid := range lineIds {
		if in.LineSnapshots[lid] == nil {
			return EvaluateResult{Hit: false}
		}
	}

	// 初始化 lane 能力（全部为 true，后续按 line 做按位与）
	capLane := CapabilityBits{true, true, true, true}

	for _, lid := range lineIds {
		snap := in.LineSnapshots[lid]

		// Phase 1: Route 判定
		if !routeMatch(snap, in.RouteMode, in.FromAreaIDs, in.ToAreaIDs) {
			// Route 不可达，所有能力置 false
			capLane.CanPickup = false
			capLane.CanCodPickup = false
			capLane.CanDeliver = false
			capLane.CanCodDeliver = false
		}

		// Phase 2: Origin/Dest 能力判定
		if capLane.CanPickup || capLane.CanCodPickup {
			// 检查 Origin 能力
			if snap.Origin != nil {
				originMatch := originCapabilityMatch(snap.Origin, in.OriginKey, in.IsCOD)
				if !originMatch.CanPickup {
					capLane.CanPickup = false
				}
				if !originMatch.CanCodPickup {
					capLane.CanCodPickup = false
				}
			}
		}

		if capLane.CanDeliver || capLane.CanCodDeliver {
			// 检查 Dest 能力
			if snap.Dest != nil {
				destMatch := destCapabilityMatch(snap.Dest, in.DestKey, in.IsCOD)
				if !destMatch.CanDeliver {
					capLane.CanDeliver = false
				}
				if !destMatch.CanCodDeliver {
					capLane.CanCodDeliver = false
				}
			}
		}

		// 提前剪枝：如果已经全 false，无需继续
		if !capLane.CanPickup && !capLane.CanCodPickup && !capLane.CanDeliver && !capLane.CanCodDeliver {
			break
		}
	}

	return EvaluateResult{
		Hit: true,
		Serviceable: &pb.LaneAreaServiceable{
			LaneCode:    &in.LaneCode,
			Serviceable: buildAreaServiceable(capLane),
		},
	}
}

// extractLineIDsFromLane 从 lane 的 compose 中提取 lineId 列表
func extractLineIDsFromLane(laneInfo *lfs_service.LaneCodeStruct) []string {
	var lineIds []string
	for _, compose := range laneInfo.Composes {
		if compose.ResourceType == constant.LaneComposeLine {
			lineIds = append(lineIds, compose.ResourceID)
		}
	}
	return lineIds
}

// routeMatch 判断路由是否匹配（与现有 checkLocationServiceableRouteV2 逻辑等价）
func routeMatch(snap *LineSnapshot, routeMode uint8, fromAreas, toAreas []uint64) bool {
	if routeMode == constant.ROUTE_UNSUPPORTED {
		// 黑名单判定
		if snap.RouteBlack == nil {
			return true // 没有黑名单配置，默认可达
		}

		// 检查是否命中黑名单（移除GlobalBlocked的错误短路）
		for _, from := range fromAreas {
			set := snap.RouteBlack.FromTo[from]
			if set == nil {
				continue // 该 from 没有黑名单配置，检查下一个
			}

			// 检查是否整个 from 被拉黑（占位符）
			if set.Contains(uint32(constant.ToAreaPlaceholder)) {
				return false // from 整体被拉黑，不可达
			}

			// 检查具体的 from→to 是否被拉黑
			for _, to := range toAreas {
				if set.Contains(uint32(to)) {
					return false // 命中黑名单，不可达
				}
			}
		}
		return true // 未命中黑名单，可达
	}

	// 白名单判定
	if snap.RouteWhite == nil {
		return false
	}

	for _, from := range fromAreas {
		if set := snap.RouteWhite.FromTo[from]; set != nil {
			for _, to := range toAreas {
				if set.Contains(uint32(to)) {
					return true // 命中白名单，可达
				}
			}
		}
	}
	return false // 未命中白名单，不可达
}

// buildAreaServiceable 构建 AreaServiceable 响应
func buildAreaServiceable(cap CapabilityBits) *pb.AreaServiceable {
	// 仿照现有的 successAreaServiceable() 函数
	return &pb.AreaServiceable{
		Code:    proto.Int32(lcos_error.SuccessCode),
		Message: proto.String("success"),
		Ability: &pb.AreaServiceability{
			CanPickup:     proto.Uint32(boolToUint32(cap.CanPickup)),
			CanCodPickup:  proto.Uint32(boolToUint32(cap.CanCodPickup)),
			CanDeliver:    proto.Uint32(boolToUint32(cap.CanDeliver)),
			CanCodDeliver: proto.Uint32(boolToUint32(cap.CanCodDeliver)),
			// 其他字段保持默认值或根据需要补充
			PickupInEFence:  proto.Uint32(uint32(constant.ENABLED)),
			DeliverInEFence: proto.Uint32(uint32(constant.ENABLED)),
		},
		ActualPoints: []*pb.ActualPoint{}, // Phase 1 暂不处理 ActualPoints
	}
}

// boolToUint32 布尔值转换为 uint32
func boolToUint32(b bool) uint32 {
	if b {
		return uint32(constant.ENABLED)
	}
	return uint32(constant.DISABLED)
}

// originCapabilityMatch 检查 Origin 能力匹配
func originCapabilityMatch(origin *OriginAgg, key uint64, isCOD bool) CapabilityBits {
	result := CapabilityBits{false, false, false, false}

	// 检查普通 pickup 能力
	if origin.Allowed != nil && origin.Allowed.Contains(uint32(key)) {
		result.CanPickup = true
	}

	// 检查 COD pickup 能力
	if origin.AllowedCod != nil && origin.AllowedCod.Contains(uint32(key)) {
		result.CanCodPickup = true
	} else if origin.Allowed != nil && origin.Allowed.Contains(uint32(key)) {
		// 如果没有专门的 COD 位图，但普通位图允许，则 COD 也允许
		result.CanCodPickup = true
	}

	return result
}

// destCapabilityMatch 检查 Dest 能力匹配
func destCapabilityMatch(dest *DestAgg, key uint64, isCOD bool) CapabilityBits {
	result := CapabilityBits{false, false, false, false}

	// 检查普通 deliver 能力
	if dest.Allowed != nil && dest.Allowed.Contains(uint32(key)) {
		result.CanDeliver = true
	}

	// 检查 COD deliver 能力
	if dest.AllowedCod != nil && dest.AllowedCod.Contains(uint32(key)) {
		result.CanCodDeliver = true
	} else if dest.Allowed != nil && dest.Allowed.Contains(uint32(key)) {
		// 如果没有专门的 COD 位图，但普通位图允许，则 COD 也允许
		result.CanCodDeliver = true
	}

	return result
}

// containsAgg 检查 Origin/Dest 聚合是否包含指定 key（兼容性方法）
func containsAgg(a *OriginAgg, key uint64, isCOD bool) bool {
	if isCOD && a.AllowedCod != nil {
		return a.AllowedCod.Contains(uint32(key))
	}
	return a.Allowed != nil && a.Allowed.Contains(uint32(key))
}

func containsDestAgg(a *DestAgg, key uint64, isCOD bool) bool {
	if isCOD && a.AllowedCod != nil {
		return a.AllowedCod.Contains(uint32(key))
	}
	return a.Allowed != nil && a.Allowed.Contains(uint32(key))
}
