package fastpath

import (
	"testing"

	"github.com/RoaringBitmap/roaring"
	"github.com/stretchr/testify/assert"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lfs_service"
)

func TestEvaluateLaneByBitmap_RouteWhitelist(t *testing.T) {
	evaluator := NewEvaluator()

	// 构造测试数据
	lineSnapshots := map[string]*LineSnapshot{
		"line1": {
			LineID:                "line1",
			CollectDeliverGroupID: "group1",
			RouteWhite: &RouteWhitelistAgg{
				FromTo: map[uint64]*roaring.Bitmap{
					100: func() *roaring.Bitmap {
						bitmap := roaring.New()
						bitmap.Add(200) // from area 100 可达 to area 200
						return bitmap
					}(),
				},
			},
		},
	}

	laneInfo := &lfs_service.LaneCodeStruct{
		LaneCode: "LANE001",
		Composes: []*lfs_service.SingleCompose{
			{
				ResourceType: constant.LaneComposeLine,
				ResourceID:   "line1",
			},
		},
	}

	// 测试用例1：命中白名单，应该可达
	input := EvaluateInput{
		LaneCode:      "LANE001",
		LaneInfo:      laneInfo,
		LineSnapshots: lineSnapshots,
		FromAreaIDs:   []uint64{100},
		ToAreaIDs:     []uint64{200},
		RouteMode:     constant.ROUTE_SUPPORTED,
	}

	result := evaluator.EvaluateLaneByBitmap(input)
	assert.True(t, result.Hit)
	assert.NotNil(t, result.Serviceable)
	assert.Equal(t, "LANE001", *result.Serviceable.LaneCode)

	// 测试用例2：未命中白名单，应该不可达
	input.ToAreaIDs = []uint64{999} // 不在白名单中
	result = evaluator.EvaluateLaneByBitmap(input)
	assert.True(t, result.Hit)
	// 路由不可达，所有能力应该为 false
	assert.Equal(t, uint32(constant.DISABLED), *result.Serviceable.Serviceable.Ability.CanPickup)
}

func TestEvaluateLaneByBitmap_RouteBlacklist(t *testing.T) {
	evaluator := NewEvaluator()

	// 构造黑名单测试数据
	lineSnapshots := map[string]*LineSnapshot{
		"line1": {
			LineID:                "line1",
			CollectDeliverGroupID: "group1",
			RouteBlack: &RouteBlacklistAgg{
				GlobalBlocked: true,
				FromTo: map[uint64]*roaring.Bitmap{
					100: func() *roaring.Bitmap {
						bitmap := roaring.New()
						bitmap.Add(uint32(constant.ToAreaPlaceholder)) // 存在 from area 100 的占位符
						bitmap.Add(200)                                // from area 100 -> to area 200 被拉黑
						return bitmap
					}(),
				},
			},
		},
	}

	laneInfo := &lfs_service.LaneCodeStruct{
		LaneCode: "LANE001",
		Composes: []*lfs_service.SingleCompose{
			{
				ResourceType: constant.LaneComposeLine,
				ResourceID:   "line1",
			},
		},
	}

	// 测试用例：命中黑名单，应该不可达
	input := EvaluateInput{
		LaneCode:      "LANE001",
		LaneInfo:      laneInfo,
		LineSnapshots: lineSnapshots,
		FromAreaIDs:   []uint64{100},
		ToAreaIDs:     []uint64{200},
		RouteMode:     constant.ROUTE_UNSUPPORTED, // 黑名单模式
	}

	result := evaluator.EvaluateLaneByBitmap(input)
	assert.True(t, result.Hit)
	// 命中黑名单，所有能力应该为 false
	assert.Equal(t, uint32(constant.DISABLED), *result.Serviceable.Serviceable.Ability.CanPickup)
}

func TestEvaluateLaneByBitmap_MissingSnapshot(t *testing.T) {
	evaluator := NewEvaluator()

	laneInfo := &lfs_service.LaneCodeStruct{
		LaneCode: "LANE001",
		Composes: []*lfs_service.SingleCompose{
			{
				ResourceType: constant.LaneComposeLine,
				ResourceID:   "missing_line", // 这个 line 没有快照
			},
		},
	}

	input := EvaluateInput{
		LaneCode:      "LANE001",
		LaneInfo:      laneInfo,
		LineSnapshots: map[string]*LineSnapshot{}, // 空的快照
	}

	result := evaluator.EvaluateLaneByBitmap(input)
	assert.False(t, result.Hit) // 应该返回未命中
}

func TestExtractLineIDsFromLane(t *testing.T) {
	laneInfo := &lfs_service.LaneCodeStruct{
		Composes: []*lfs_service.SingleCompose{
			{
				ResourceType: constant.LaneComposeLine,
				ResourceID:   "line1",
			},
			{
				ResourceType: constant.LaneComposeSite,
				ResourceID:   "site1", // 这个应该被忽略
			},
			{
				ResourceType: constant.LaneComposeLine,
				ResourceID:   "line2",
			},
		},
	}

	lineIds := extractLineIDsFromLane(laneInfo)
	expected := []string{"line1", "line2"}
	assert.Equal(t, expected, lineIds)
}

func TestRouteMatch_Whitelist(t *testing.T) {
	snap := &LineSnapshot{
		RouteWhite: &RouteWhitelistAgg{
			FromTo: map[uint64]*roaring.Bitmap{
				100: func() *roaring.Bitmap {
					bitmap := roaring.New()
					bitmap.Add(200)
					bitmap.Add(201)
					return bitmap
				}(),
				101: func() *roaring.Bitmap {
					bitmap := roaring.New()
					bitmap.Add(300)
					return bitmap
				}(),
			},
		},
	}

	// 测试命中情况
	assert.True(t, routeMatch(snap, constant.ROUTE_SUPPORTED, []uint64{100}, []uint64{200}))
	assert.True(t, routeMatch(snap, constant.ROUTE_SUPPORTED, []uint64{100}, []uint64{201}))
	assert.True(t, routeMatch(snap, constant.ROUTE_SUPPORTED, []uint64{101}, []uint64{300}))

	// 测试未命中情况
	assert.False(t, routeMatch(snap, constant.ROUTE_SUPPORTED, []uint64{100}, []uint64{999}))
	assert.False(t, routeMatch(snap, constant.ROUTE_SUPPORTED, []uint64{999}, []uint64{200}))
}

func TestRouteMatch_Blacklist(t *testing.T) {
	snap := &LineSnapshot{
		RouteBlack: &RouteBlacklistAgg{
			GlobalBlocked: true,
			FromTo: map[uint64]*roaring.Bitmap{
				100: func() *roaring.Bitmap {
					bitmap := roaring.New()
					bitmap.Add(uint32(constant.ToAreaPlaceholder)) // fromArea 100 整体被拉黑
					bitmap.Add(200)                                // 具体拉黑 100 -> 200 (实际上由于整体拉黑，这个是多余的)
					return bitmap
				}(),
			},
		},
	}

	// 测试被拉黑的路径：由于fromArea 100整体被拉黑，所有从100出发的路由都不可达
	assert.False(t, routeMatch(snap, constant.ROUTE_UNSUPPORTED, []uint64{100}, []uint64{200}))
	assert.False(t, routeMatch(snap, constant.ROUTE_UNSUPPORTED, []uint64{100}, []uint64{201})) // 修正：整体拉黑

	// 测试未被拉黑的路径：其他fromArea应该可达
	assert.True(t, routeMatch(snap, constant.ROUTE_UNSUPPORTED, []uint64{101}, []uint64{200}))
}

func TestRouteMatch_BlacklistWithoutGlobalBlocked(t *testing.T) {
	// 测试修复后的逻辑：即使GlobalBlocked=false，也应该检查具体的FromTo黑名单
	snap := &LineSnapshot{
		RouteBlack: &RouteBlacklistAgg{
			GlobalBlocked: false, // 关键：全局未标记阻止
			FromTo: map[uint64]*roaring.Bitmap{
				100: func() *roaring.Bitmap {
					bitmap := roaring.New()
					bitmap.Add(200) // 拉黑 100 -> 200
					return bitmap
				}(),
			},
		},
	}

	// 测试被拉黑的路径：应该不可达（修复前这里会错误地返回true）
	assert.False(t, routeMatch(snap, constant.ROUTE_UNSUPPORTED, []uint64{100}, []uint64{200}))

	// 测试未被拉黑的路径：应该可达
	assert.True(t, routeMatch(snap, constant.ROUTE_UNSUPPORTED, []uint64{100}, []uint64{201}))
	assert.True(t, routeMatch(snap, constant.ROUTE_UNSUPPORTED, []uint64{101}, []uint64{200}))
}

func TestOriginDestCapabilityMatch(t *testing.T) {
	// Test Origin capability match
	origin := OriginAgg{
		Kind: DimLocationID,
		Allowed: func() *roaring.Bitmap {
			bitmap := roaring.New()
			bitmap.Add(100) // Location 100 支持普通pickup
			bitmap.Add(200) // Location 200 支持普通pickup
			return bitmap
		}(),
		AllowedCod: func() *roaring.Bitmap {
			bitmap := roaring.New()
			bitmap.Add(100) // Location 100 支持COD pickup
			// Location 200 不支持COD pickup
			return bitmap
		}(),
	}

	// 测试普通pickup能力
	result1 := originCapabilityMatch(&origin, 100, false)
	assert.True(t, result1.CanPickup)
	assert.True(t, result1.CanCodPickup)

	result2 := originCapabilityMatch(&origin, 200, false)
	assert.True(t, result2.CanPickup)
	assert.True(t, result2.CanCodPickup) // 没有专门COD配置时，默认允许

	result3 := originCapabilityMatch(&origin, 300, false)
	assert.False(t, result3.CanPickup)
	assert.False(t, result3.CanCodPickup)

	// Test Dest capability match
	dest := &DestAgg{
		Kind: DimLocationID,
		Allowed: func() *roaring.Bitmap {
			bitmap := roaring.New()
			bitmap.Add(100) // Location 100 支持普通deliver
			bitmap.Add(200) // Location 200 支持普通deliver
			return bitmap
		}(),
		AllowedCod: func() *roaring.Bitmap {
			bitmap := roaring.New()
			bitmap.Add(100) // Location 100 支持COD deliver
			return bitmap
		}(),
	}

	result4 := destCapabilityMatch(dest, 100, false)
	assert.True(t, result4.CanDeliver)
	assert.True(t, result4.CanCodDeliver)

	result5 := destCapabilityMatch(dest, 200, false)
	assert.True(t, result5.CanDeliver)
	assert.True(t, result5.CanCodDeliver) // 没有专门COD配置时，默认允许

	result6 := destCapabilityMatch(dest, 300, false)
	assert.False(t, result6.CanDeliver)
	assert.False(t, result6.CanCodDeliver)
}

func TestEvaluateLaneByBitmap_Phase2_OriginDestComplete(t *testing.T) {
	evaluator := NewEvaluator()

	// 构造完整的Phase 2测试数据
	lineSnapshots := map[string]*LineSnapshot{
		"line1": {
			LineID:                "line1",
			CollectDeliverGroupID: "group1",
			// Route允许：100->200可达
			RouteWhite: &RouteWhitelistAgg{
				FromTo: map[uint64]*roaring.Bitmap{
					100: func() *roaring.Bitmap {
						bitmap := roaring.New()
						bitmap.Add(200)
						return bitmap
					}(),
				},
			},
			// Origin能力：location 100支持pickup
			Origin: &OriginAgg{
				Kind: DimLocationID,
				Allowed: func() *roaring.Bitmap {
					bitmap := roaring.New()
					bitmap.Add(100)
					return bitmap
				}(),
				AllowedCod: func() *roaring.Bitmap {
					bitmap := roaring.New()
					bitmap.Add(100)
					return bitmap
				}(),
			},
			// Dest能力：location 200支持deliver
			Dest: &DestAgg{
				Kind: DimLocationID,
				Allowed: func() *roaring.Bitmap {
					bitmap := roaring.New()
					bitmap.Add(200)
					return bitmap
				}(),
				AllowedCod: func() *roaring.Bitmap {
					bitmap := roaring.New()
					bitmap.Add(200)
					return bitmap
				}(),
			},
		},
	}

	laneInfo := &lfs_service.LaneCodeStruct{
		LaneCode: "LANE001",
		Composes: []*lfs_service.SingleCompose{
			{
				ResourceType: constant.LaneComposeLine,
				ResourceID:   "line1",
			},
		},
	}

	input := EvaluateInput{
		LaneCode:      "LANE001",
		LaneInfo:      laneInfo,
		LineSnapshots: lineSnapshots,
		OriginKey:     100, // 对应Origin location
		DestKey:       200, // 对应Dest location
		FromAreaIDs:   []uint64{100},
		ToAreaIDs:     []uint64{200},
		RouteMode:     constant.ROUTE_SUPPORTED, // 白名单模式
		IsCOD:         false,
	}

	result := evaluator.EvaluateLaneByBitmap(input)
	assert.True(t, result.Hit)
	assert.NotNil(t, result.Serviceable)
	assert.Equal(t, uint32(constant.ENABLED), *result.Serviceable.Serviceable.Ability.CanPickup)
	assert.Equal(t, uint32(constant.ENABLED), *result.Serviceable.Serviceable.Ability.CanCodPickup)
	assert.Equal(t, uint32(constant.ENABLED), *result.Serviceable.Serviceable.Ability.CanDeliver)
	assert.Equal(t, uint32(constant.ENABLED), *result.Serviceable.Serviceable.Ability.CanCodDeliver)
}

func TestEvaluateLaneByBitmap_Phase2_OriginFail(t *testing.T) {
	evaluator := NewEvaluator()

	lineSnapshots := map[string]*LineSnapshot{
		"line1": {
			LineID:                "line1",
			CollectDeliverGroupID: "group1",
			// Route允许：100->200可达
			RouteWhite: &RouteWhitelistAgg{
				FromTo: map[uint64]*roaring.Bitmap{
					100: func() *roaring.Bitmap {
						bitmap := roaring.New()
						bitmap.Add(200)
						return bitmap
					}(),
				},
			},
			// Origin能力：location 100不支持pickup（空位图）
			Origin: &OriginAgg{
				Kind:       DimLocationID,
				Allowed:    roaring.New(), // 空位图，不支持任何pickup
				AllowedCod: roaring.New(),
			},
			// Dest能力：location 200支持deliver
			Dest: &DestAgg{
				Kind: DimLocationID,
				Allowed: func() *roaring.Bitmap {
					bitmap := roaring.New()
					bitmap.Add(200)
					return bitmap
				}(),
				AllowedCod: func() *roaring.Bitmap {
					bitmap := roaring.New()
					bitmap.Add(200)
					return bitmap
				}(),
			},
		},
	}

	laneInfo := &lfs_service.LaneCodeStruct{
		LaneCode: "LANE001",
		Composes: []*lfs_service.SingleCompose{
			{
				ResourceType: constant.LaneComposeLine,
				ResourceID:   "line1",
			},
		},
	}

	input := EvaluateInput{
		LaneCode:      "LANE001",
		LaneInfo:      laneInfo,
		LineSnapshots: lineSnapshots,
		OriginKey:     100,
		DestKey:       200,
		FromAreaIDs:   []uint64{100},
		ToAreaIDs:     []uint64{200},
		RouteMode:     constant.ROUTE_SUPPORTED,
		IsCOD:         false,
	}

	result := evaluator.EvaluateLaneByBitmap(input)
	assert.True(t, result.Hit)
	assert.NotNil(t, result.Serviceable)
	// Origin不支持，pickup应该为DISABLED
	assert.Equal(t, uint32(constant.DISABLED), *result.Serviceable.Serviceable.Ability.CanPickup)
	assert.Equal(t, uint32(constant.DISABLED), *result.Serviceable.Serviceable.Ability.CanCodPickup)
	// Dest支持，deliver应该为ENABLED
	assert.Equal(t, uint32(constant.ENABLED), *result.Serviceable.Serviceable.Ability.CanDeliver)
	assert.Equal(t, uint32(constant.ENABLED), *result.Serviceable.Serviceable.Ability.CanCodDeliver)
}

func TestEvaluateLaneByBitmap_Phase2_DestFail(t *testing.T) {
	evaluator := NewEvaluator()

	lineSnapshots := map[string]*LineSnapshot{
		"line1": {
			LineID:                "line1",
			CollectDeliverGroupID: "group1",
			// Route允许：100->200可达
			RouteWhite: &RouteWhitelistAgg{
				FromTo: map[uint64]*roaring.Bitmap{
					100: func() *roaring.Bitmap {
						bitmap := roaring.New()
						bitmap.Add(200)
						return bitmap
					}(),
				},
			},
			// Origin能力：location 100支持pickup
			Origin: &OriginAgg{
				Kind: DimLocationID,
				Allowed: func() *roaring.Bitmap {
					bitmap := roaring.New()
					bitmap.Add(100)
					return bitmap
				}(),
				AllowedCod: func() *roaring.Bitmap {
					bitmap := roaring.New()
					bitmap.Add(100)
					return bitmap
				}(),
			},
			// Dest能力：location 200不支持deliver（空位图）
			Dest: &DestAgg{
				Kind:       DimLocationID,
				Allowed:    roaring.New(), // 空位图，不支持任何deliver
				AllowedCod: roaring.New(),
			},
		},
	}

	laneInfo := &lfs_service.LaneCodeStruct{
		LaneCode: "LANE001",
		Composes: []*lfs_service.SingleCompose{
			{
				ResourceType: constant.LaneComposeLine,
				ResourceID:   "line1",
			},
		},
	}

	input := EvaluateInput{
		LaneCode:      "LANE001",
		LaneInfo:      laneInfo,
		LineSnapshots: lineSnapshots,
		OriginKey:     100,
		DestKey:       200,
		FromAreaIDs:   []uint64{100},
		ToAreaIDs:     []uint64{200},
		RouteMode:     constant.ROUTE_SUPPORTED,
		IsCOD:         false,
	}

	result := evaluator.EvaluateLaneByBitmap(input)
	assert.True(t, result.Hit)
	assert.NotNil(t, result.Serviceable)
	// Origin支持，pickup应该为ENABLED
	assert.Equal(t, uint32(constant.ENABLED), *result.Serviceable.Serviceable.Ability.CanPickup)
	assert.Equal(t, uint32(constant.ENABLED), *result.Serviceable.Serviceable.Ability.CanCodPickup)
	// Dest不支持，deliver应该为DISABLED
	assert.Equal(t, uint32(constant.DISABLED), *result.Serviceable.Serviceable.Ability.CanDeliver)
	assert.Equal(t, uint32(constant.DISABLED), *result.Serviceable.Serviceable.Ability.CanCodDeliver)
}

func TestEvaluateLaneByBitmap_Phase2_MultipleLines(t *testing.T) {
	evaluator := NewEvaluator()

	// 构造多个line的场景：line1支持所有能力，line2不支持pickup
	lineSnapshots := map[string]*LineSnapshot{
		"line1": {
			LineID:                "line1",
			CollectDeliverGroupID: "group1",
			RouteWhite: &RouteWhitelistAgg{
				FromTo: map[uint64]*roaring.Bitmap{
					100: func() *roaring.Bitmap {
						bitmap := roaring.New()
						bitmap.Add(200)
						return bitmap
					}(),
				},
			},
			Origin: &OriginAgg{
				Kind: DimLocationID,
				Allowed: func() *roaring.Bitmap {
					bitmap := roaring.New()
					bitmap.Add(100)
					return bitmap
				}(),
				AllowedCod: func() *roaring.Bitmap {
					bitmap := roaring.New()
					bitmap.Add(100)
					return bitmap
				}(),
			},
			Dest: &DestAgg{
				Kind: DimLocationID,
				Allowed: func() *roaring.Bitmap {
					bitmap := roaring.New()
					bitmap.Add(200)
					return bitmap
				}(),
				AllowedCod: func() *roaring.Bitmap {
					bitmap := roaring.New()
					bitmap.Add(200)
					return bitmap
				}(),
			},
		},
		"line2": {
			LineID:                "line2",
			CollectDeliverGroupID: "group1",
			RouteWhite: &RouteWhitelistAgg{
				FromTo: map[uint64]*roaring.Bitmap{
					100: func() *roaring.Bitmap {
						bitmap := roaring.New()
						bitmap.Add(200)
						return bitmap
					}(),
				},
			},
			Origin: &OriginAgg{
				Kind:       DimLocationID,
				Allowed:    roaring.New(), // line2不支持pickup
				AllowedCod: roaring.New(),
			},
			Dest: &DestAgg{
				Kind: DimLocationID,
				Allowed: func() *roaring.Bitmap {
					bitmap := roaring.New()
					bitmap.Add(200)
					return bitmap
				}(),
				AllowedCod: func() *roaring.Bitmap {
					bitmap := roaring.New()
					bitmap.Add(200)
					return bitmap
				}(),
			},
		},
	}

	laneInfo := &lfs_service.LaneCodeStruct{
		LaneCode: "LANE001",
		Composes: []*lfs_service.SingleCompose{
			{
				ResourceType: constant.LaneComposeLine,
				ResourceID:   "line1",
			},
			{
				ResourceType: constant.LaneComposeLine,
				ResourceID:   "line2",
			},
		},
	}

	input := EvaluateInput{
		LaneCode:      "LANE001",
		LaneInfo:      laneInfo,
		LineSnapshots: lineSnapshots,
		OriginKey:     100,
		DestKey:       200,
		FromAreaIDs:   []uint64{100},
		ToAreaIDs:     []uint64{200},
		RouteMode:     constant.ROUTE_SUPPORTED,
		IsCOD:         false,
	}

	result := evaluator.EvaluateLaneByBitmap(input)
	assert.True(t, result.Hit)
	assert.NotNil(t, result.Serviceable)
	// 由于line2不支持pickup，最终结果应该是pickup为DISABLED（按位与操作）
	assert.Equal(t, uint32(constant.DISABLED), *result.Serviceable.Serviceable.Ability.CanPickup)
	assert.Equal(t, uint32(constant.DISABLED), *result.Serviceable.Serviceable.Ability.CanCodPickup)
	// 两个line都支持deliver，所以deliver为ENABLED
	assert.Equal(t, uint32(constant.ENABLED), *result.Serviceable.Serviceable.Ability.CanDeliver)
	assert.Equal(t, uint32(constant.ENABLED), *result.Serviceable.Serviceable.Ability.CanCodDeliver)
}

func TestKeyNormalizer_NormalizeAddressKey(t *testing.T) {
	normalizer := NewKeyNormalizer()

	// 测试邮编清理功能
	result := normalizer.sanitizePostcode("12345-678")
	assert.Equal(t, "12345678", result)

	result2 := normalizer.sanitizePostcode("100-001")
	assert.Equal(t, "100001", result2)

	// 测试巴西CEP识别
	assert.True(t, normalizer.looksLikeBrazilianCEP("12345678"))
	assert.False(t, normalizer.looksLikeBrazilianCEP("12345"))
}
