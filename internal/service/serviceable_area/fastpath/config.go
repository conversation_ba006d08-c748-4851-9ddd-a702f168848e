package fastpath

import (
	"context"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/config"
)

// Config 位图快速路径配置
type Config struct {
	Enabled       bool    `json:"enabled"`        // 总开关，启用位图快速路径
	HitRateThres  float64 `json:"hit_rate_thres"` // 命中率阈值
	TimeoutMs     int     `json:"timeout_ms"`     // 超时毫秒
	MaxBatchSize  int     `json:"max_batch_size"` // 最大批处理大小
	EnableMetrics bool    `json:"enable_metrics"` // 是否启用详细指标
}

// DefaultConfig 返回默认配置
func DefaultConfig() Config {
	return Config{
		Enabled:       false, // 默认关闭
		HitRateThres:  0.1,   // 10% 命中率阈值
		TimeoutMs:     50,    // 50ms 超时
		MaxBatchSize:  1000,  // 最大 1000 个 line
		EnableMetrics: true,
	}
}

// 使用统一的配置管理，配置键定义在 internal/config/config.go 中

// GetConfigFromContext 从 Apollo 配置中心获取配置
func GetConfigFromContext(ctx context.Context) Config {
	cfg := DefaultConfig()

	// 使用统一配置管理获取配置
	cfg.Enabled = config.GetFastPathBitmapEnabled(ctx)
	cfg.TimeoutMs = config.GetFastPathBitmapTimeout(ctx)
	cfg.MaxBatchSize = config.GetFastPathBitmapMaxBatchSize(ctx)
	cfg.HitRateThres = config.GetFastPathBitmapHitRateThres(ctx)
	cfg.EnableMetrics = config.GetFastPathBitmapEnableMetrics(ctx)

	return cfg
}

// GetEnabled 获取位图快速路径是否启用
func GetEnabled(ctx context.Context) bool {
	return config.GetFastPathBitmapEnabled(ctx)
}

// IsLocationOnlySupported 检查是否只支持Location维度
// Postcode/Cep维度流量少，实现复杂度高，直接走老流程
func IsLocationOnlySupported() bool {
	return true // 当前只支持Location维度
}

// GetTimeout 获取超时配置（毫秒）
func GetTimeout(ctx context.Context) int {
	return config.GetFastPathBitmapTimeout(ctx)
}

// GetMaxBatchSize 获取最大批处理大小
func GetMaxBatchSize(ctx context.Context) int {
	return config.GetFastPathBitmapMaxBatchSize(ctx)
}

// LogConfigChange 记录配置变更（用于监听器）
func LogConfigChange(ctx context.Context, key string, oldValue, newValue interface{}) {
	logger.CtxLogInfof(ctx, "Bitmap FastPath config changed: key=%s, old=%v, new=%v", key, oldValue, newValue)
}

// ValidateConfig 验证配置有效性
func ValidateConfig(cfg Config) error {
	if cfg.TimeoutMs <= 0 {
		cfg.TimeoutMs = 50
	}
	if cfg.MaxBatchSize <= 0 {
		cfg.MaxBatchSize = 1000
	}
	if cfg.HitRateThres < 0 {
		cfg.HitRateThres = 0.1
	}
	return nil
}

// GetApolloFastPathConfig 从 Apollo 获取完整的快速路径配置
func GetApolloFastPathConfig(ctx context.Context) (Config, error) {
	cfg := GetConfigFromContext(ctx)

	// 验证配置
	if err := ValidateConfig(cfg); err != nil {
		logger.CtxLogErrorf(ctx, "FastPath config validation failed: %v", err)
		return DefaultConfig(), err
	}

	return cfg, nil
}

// UpdateConfig 动态更新配置（通过 Apollo 配置变更触发）
func UpdateConfig(ctx context.Context, newCfg Config) error {
	// 验证新配置
	if err := ValidateConfig(newCfg); err != nil {
		return err
	}

	logger.CtxLogInfof(ctx, "FastPath config updated: %+v", newCfg)
	return nil
}
