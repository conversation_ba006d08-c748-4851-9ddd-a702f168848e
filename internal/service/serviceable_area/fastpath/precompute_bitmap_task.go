package fastpath

import (
	"context"
	"sync"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
)

// PrecomputeBitmapTask bitmap预计算任务
// 定时重建RoaringBitmap，内存高效地存储大量Product+Address组合
type PrecomputeBitmapTask struct {
	bitmapProvider ProductAddressBitmapProvider

	// 配置
	refreshInterval time.Duration // 刷新间隔
	enabledRegions  []string      // 启用的地区列表

	// 状态控制
	isRunning bool
	stopChan  chan struct{}
	ticker    *time.Ticker
	mutex     sync.RWMutex

	// 统计信息
	lastRefreshTime time.Time
	totalRebuilt    int64
	errorCount      int64
}

// PrecomputeBitmapStats bitmap预计算统计信息
type PrecomputeBitmapStats struct {
	IsRunning       bool      `json:"is_running"`
	LastRefreshTime time.Time `json:"last_refresh_time"`
	TotalRebuilt    int64     `json:"total_rebuilt"`
	ErrorCount      int64     `json:"error_count"`
	NextRefreshTime time.Time `json:"next_refresh_time"`
	EnabledRegions  []string  `json:"enabled_regions"`
}

// NewPrecomputeBitmapTask 创建bitmap预计算任务
func NewPrecomputeBitmapTask(
	snapshotProvider SnapshotProvider,
) *PrecomputeBitmapTask {
	bitmapProvider := NewProductAddressBitmapProvider(snapshotProvider)

	return &PrecomputeBitmapTask{
		bitmapProvider:  bitmapProvider,
		refreshInterval: 5 * time.Minute,                                    // 5分钟刷新
		enabledRegions:  []string{"SG", "MY", "TH", "VN", "PH", "ID", "TW"}, // 默认地区
		stopChan:        make(chan struct{}),
	}
}

// Start 启动bitmap预计算任务
func (task *PrecomputeBitmapTask) Start(ctx context.Context) error {
	task.mutex.Lock()
	defer task.mutex.Unlock()

	if task.isRunning {
		return nil // 已经在运行
	}

	task.isRunning = true
	task.ticker = time.NewTicker(task.refreshInterval)

	logger.LogInfof("Starting bitmap precompute task with interval %v for regions: %v",
		task.refreshInterval, task.enabledRegions)

	// 立即执行一次预计算
	go func() {
		task.executePrecompute(ctx)

		// 定时执行
		for {
			select {
			case <-task.ticker.C:
				task.executePrecompute(ctx)
			case <-task.stopChan:
				logger.LogInfof("Bitmap precompute task stopped")
				return
			}
		}
	}()

	return nil
}

// Stop 停止bitmap预计算任务
func (task *PrecomputeBitmapTask) Stop() {
	task.mutex.Lock()
	defer task.mutex.Unlock()

	if !task.isRunning {
		return
	}

	task.isRunning = false
	close(task.stopChan)
	if task.ticker != nil {
		task.ticker.Stop()
	}

	logger.LogInfof("Bitmap precompute task stop requested")
}

// executePrecompute 执行bitmap预计算
func (task *PrecomputeBitmapTask) executePrecompute(ctx context.Context) {
	startTime := time.Now()
	lcosCtx := utils.NewCommonCtx(ctx)

	logger.LogInfof("Starting bitmap precompute for regions: %v", task.enabledRegions)

	var totalRebuilt int64
	var errorCount int64

	// 并发重建各个地区的bitmap
	var wg sync.WaitGroup
	errorChan := make(chan error, len(task.enabledRegions))

	for _, region := range task.enabledRegions {
		wg.Add(1)
		go func(r string) {
			defer wg.Done()

			regionStart := time.Now()
			logger.LogInfof("Rebuilding bitmap for region %s", r)

			if err := task.bitmapProvider.RebuildBitmap(lcosCtx, r); err != nil {
				logger.LogErrorf("Failed to rebuild bitmap for region %s: %v", r, err)
				errorChan <- err
			} else {
				logger.LogInfof("Successfully rebuilt bitmap for region %s, duration: %v",
					r, time.Since(regionStart))
				// 原子操作更新计数
				task.incrementRebuiltCount()
			}
		}(region)
	}

	// 等待所有地区完成
	wg.Wait()
	close(errorChan)

	// 统计错误
	for err := range errorChan {
		if err != nil {
			errorCount++
		}
	}

	// 更新统计信息
	task.mutex.Lock()
	task.lastRefreshTime = time.Now()
	task.totalRebuilt += totalRebuilt
	task.errorCount += errorCount
	task.mutex.Unlock()

	duration := time.Since(startTime)
	logger.LogInfof("Bitmap precompute completed: rebuilt=%d regions, errors=%d, duration=%v",
		len(task.enabledRegions)-int(errorCount), errorCount, duration)

	// 上报指标（安全调用）
	SafeMetrics.GaugeSet("product_address_bitmap_precompute_duration_seconds",
		duration.Seconds(), map[string]string{})
	SafeMetrics.GaugeSet("product_address_bitmap_precompute_regions",
		float64(len(task.enabledRegions)), map[string]string{})
	SafeMetrics.GaugeSet("product_address_bitmap_precompute_errors",
		float64(errorCount), map[string]string{})
}

// incrementRebuiltCount 原子递增重建计数
func (task *PrecomputeBitmapTask) incrementRebuiltCount() {
	task.mutex.Lock()
	defer task.mutex.Unlock()
	task.totalRebuilt++
}

// GetStats 获取bitmap预计算统计信息
func (task *PrecomputeBitmapTask) GetStats() PrecomputeBitmapStats {
	task.mutex.RLock()
	defer task.mutex.RUnlock()

	var nextRefreshTime time.Time
	if task.isRunning && !task.lastRefreshTime.IsZero() {
		nextRefreshTime = task.lastRefreshTime.Add(task.refreshInterval)
	}

	return PrecomputeBitmapStats{
		IsRunning:       task.isRunning,
		LastRefreshTime: task.lastRefreshTime,
		TotalRebuilt:    task.totalRebuilt,
		ErrorCount:      task.errorCount,
		NextRefreshTime: nextRefreshTime,
		EnabledRegions:  task.enabledRegions,
	}
}

// UpdateConfig 更新配置
func (task *PrecomputeBitmapTask) UpdateConfig(refreshInterval time.Duration, enabledRegions []string) {
	task.mutex.Lock()
	defer task.mutex.Unlock()

	task.refreshInterval = refreshInterval
	task.enabledRegions = enabledRegions

	// 如果正在运行，重启ticker
	if task.isRunning && task.ticker != nil {
		task.ticker.Stop()
		task.ticker = time.NewTicker(task.refreshInterval)
	}

	logger.LogInfof("Updated bitmap precompute config: interval=%v, regions=%v",
		refreshInterval, enabledRegions)
}

// GetBitmapStats 获取指定地区的bitmap统计信息
func (task *PrecomputeBitmapTask) GetBitmapStats(region string) map[string]interface{} {
	return task.bitmapProvider.GetBitmapStats(region)
}

// GetBitmapProvider 获取bitmap提供者（用于测试）
func (task *PrecomputeBitmapTask) GetBitmapProvider() ProductAddressBitmapProvider {
	return task.bitmapProvider
}
