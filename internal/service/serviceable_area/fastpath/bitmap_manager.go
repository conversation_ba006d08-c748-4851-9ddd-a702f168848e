package fastpath

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/metrics"
)

// BitmapManager 位图数据管理器
type BitmapManager struct {
	generator        *BitmapGenerator
	snapshotProvider SnapshotProvider
	config           Config

	// 数据同步控制
	refreshMutex    sync.RWMutex
	lastRefreshTime time.Time

	// 统计信息
	stats *BitmapStats
}

// BitmapStats 位图统计信息
type BitmapStats struct {
	TotalSnapshots  int64     `json:"total_snapshots"`
	LastRefreshTime time.Time `json:"last_refresh_time"`
	RefreshCount    int64     `json:"refresh_count"`
	ErrorCount      int64     `json:"error_count"`
	AvgRefreshTime  float64   `json:"avg_refresh_time_ms"`
	mutex           sync.RWMutex
}

// NewBitmapManager 创建位图管理器
func NewBitmapManager(cache *localcache.Manager) *BitmapManager {
	generator := NewBitmapGenerator(cache)
	provider := NewProvider(cache)

	return &BitmapManager{
		generator:        generator,
		snapshotProvider: provider,
		config:           DefaultConfig(),
		stats:            &BitmapStats{},
	}
}

// Initialize 初始化位图管理器
func (m *BitmapManager) Initialize(ctx context.Context) error {
	// 从 Apollo 加载配置
	cfg, err := GetApolloFastPathConfig(ctx)
	if err != nil {
		logger.LogErrorf("Failed to load FastPath config: %v", err)
		cfg = DefaultConfig()
	}
	m.config = cfg

	logger.LogInfof("BitmapManager initialized with config: %+v", cfg)
	return nil
}

// RefreshAllSnapshots 刷新所有位图快照
func (m *BitmapManager) RefreshAllSnapshots(ctx utils.LCOSContext) error {
	m.refreshMutex.Lock()
	defer m.refreshMutex.Unlock()

	startTime := time.Now()
	logger.CtxLogInfof(ctx, "Starting bitmap snapshots refresh")

	// 获取需要刷新的 line keys
	lineKeys, err := m.getAllLineKeys(ctx)
	if err != nil {
		m.updateStats(false, time.Since(startTime))
		return fmt.Errorf("failed to get line keys: %w", err)
	}

	// 批量生成快照
	if err := m.generator.BatchGenerateSnapshots(ctx, lineKeys); err != nil {
		m.updateStats(false, time.Since(startTime))
		return fmt.Errorf("failed to generate snapshots: %w", err)
	}

	// 更新统计信息
	m.updateStats(true, time.Since(startTime))
	m.lastRefreshTime = time.Now()

	// 上报指标
	m.reportMetrics(ctx, len(lineKeys), time.Since(startTime))

	logger.CtxLogInfof(ctx, "Bitmap snapshots refresh completed, processed %d lines in %v",
		len(lineKeys), time.Since(startTime))

	return nil
}

// RefreshLineSnapshots 刷新指定 line 的快照
func (m *BitmapManager) RefreshLineSnapshots(ctx utils.LCOSContext, lineKeys []LineKey) error {
	if len(lineKeys) == 0 {
		return nil
	}

	startTime := time.Now()
	logger.CtxLogInfof(ctx, "Refreshing snapshots for %d lines", len(lineKeys))

	// 生成快照
	if err := m.generator.GenerateLineRouteSnapshots(ctx, lineKeys); err != nil {
		return fmt.Errorf("failed to refresh line snapshots: %w", err)
	}

	// 上报指标
	duration := time.Since(startTime)
	metrics.CounterIncr("bitmap_snapshot_refresh_total", map[string]string{
		"type": "partial",
	})
	// 使用 GaugeSet 记录时延（更适合展示当前值）
	metrics.GaugeSet("bitmap_snapshot_refresh_duration_ms", float64(duration.Milliseconds()), map[string]string{
		"type": "partial",
	})
	// 记录处理的 line 数量
	metrics.GaugeSet("bitmap_snapshot_processed_lines", float64(len(lineKeys)), map[string]string{
		"type": "partial",
	})

	logger.CtxLogInfof(ctx, "Refreshed snapshots for %d lines in %v", len(lineKeys), duration)
	return nil
}

// InvalidateSnapshots 失效指定的快照
func (m *BitmapManager) InvalidateSnapshots(ctx utils.LCOSContext, lineKeys []LineKey) error {
	for _, key := range lineKeys {
		m.generator.invalidateLineSnapshot(ctx, key)
	}

	logger.CtxLogInfof(ctx, "Invalidated %d snapshots", len(lineKeys))
	return nil
}

// GetSnapshots 获取快照（代理到 SnapshotProvider）
func (m *BitmapManager) GetSnapshots(ctx context.Context, keys []LineKey) (*BatchSnapshot, error) {
	return m.snapshotProvider.BatchGetLineSnapshots(ctx, keys)
}

// WarmupSnapshots 预热快照
func (m *BitmapManager) WarmupSnapshots(ctx context.Context, keys []LineKey) error {
	return m.snapshotProvider.BatchWarmup(ctx, keys)
}

// GetStats 获取统计信息
func (m *BitmapManager) GetStats() *BitmapStats {
	m.stats.mutex.RLock()
	defer m.stats.mutex.RUnlock()
	return m.stats
}

// IsHealthy 检查管理器健康状态
func (m *BitmapManager) IsHealthy() bool {
	stats := m.GetStats()

	// 检查最近刷新时间（超过2小时认为不健康）
	if time.Since(stats.LastRefreshTime) > 2*time.Hour {
		return false
	}

	// 检查错误率（超过50%认为不健康）
	if stats.RefreshCount > 0 && float64(stats.ErrorCount)/float64(stats.RefreshCount) > 0.5 {
		return false
	}

	return true
}

// updateStats 更新统计信息
func (m *BitmapManager) updateStats(success bool, duration time.Duration) {
	m.stats.mutex.Lock()
	defer m.stats.mutex.Unlock()

	m.stats.RefreshCount++
	if !success {
		m.stats.ErrorCount++
	}

	// 更新平均刷新时间
	if m.stats.RefreshCount == 1 {
		m.stats.AvgRefreshTime = float64(duration.Milliseconds())
	} else {
		// 指数移动平均
		alpha := 0.1
		m.stats.AvgRefreshTime = alpha*float64(duration.Milliseconds()) + (1-alpha)*m.stats.AvgRefreshTime
	}

	m.stats.LastRefreshTime = time.Now()
}

// reportMetrics 上报监控指标
func (m *BitmapManager) reportMetrics(ctx context.Context, lineCount int, duration time.Duration) {
	// 刷新总数
	metrics.CounterIncr("bitmap_snapshot_refresh_total", map[string]string{
		"type": "full",
	})

	// 刷新耗时（使用 GaugeSet 记录时延）
	metrics.GaugeSet("bitmap_snapshot_refresh_duration_ms", float64(duration.Milliseconds()), map[string]string{
		"type": "full",
	})

	// 处理的 line 数量
	metrics.GaugeSet("bitmap_snapshot_processed_lines", float64(lineCount), map[string]string{
		"type": "full",
	})

	// 健康状态
	healthStatus := "healthy"
	if !m.IsHealthy() {
		healthStatus = "unhealthy"
	}
	metrics.CounterIncr("bitmap_manager_health_check", map[string]string{
		"status": healthStatus,
	})
}

// getAllLineKeys 获取所有需要生成快照的 line keys
func (m *BitmapManager) getAllLineKeys(ctx utils.LCOSContext) ([]LineKey, error) {
	// 这里需要从数据库或配置中获取所有活跃的 line 和 group 组合
	// 简化实现：返回一些示例数据
	// 实际实现需要查询：
	// 1. 所有活跃的 lineId
	// 2. 每个 lineId 对应的 collectDeliverGroupId

	var lineKeys []LineKey

	// 示例数据（实际实现需要从数据库查询）
	sampleLines := []string{"line_001", "line_002", "line_003"}
	sampleGroups := []string{"group_001", "group_002"}

	for _, lineId := range sampleLines {
		for _, groupId := range sampleGroups {
			lineKeys = append(lineKeys, LineKey{
				LineID:                lineId,
				CollectDeliverGroupID: groupId,
			})
		}
	}

	logger.CtxLogInfof(ctx, "Found %d line keys for snapshot generation", len(lineKeys))
	return lineKeys, nil
}

// ScheduleRefresh 调度定时刷新（用于定时任务）
func (m *BitmapManager) ScheduleRefresh(ctx context.Context, interval time.Duration) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	logger.LogInfof("Started bitmap snapshot refresh scheduler with interval %v", interval)

	for {
		select {
		case <-ticker.C:
			// 检查是否启用
			if !GetEnabled(ctx) {
				continue
			}

			// 创建 LCOS 上下文（简化实现）
			lcosCtx := ctx.(utils.LCOSContext)

			// 执行刷新
			if err := m.RefreshAllSnapshots(lcosCtx); err != nil {
				logger.LogErrorf("Scheduled refresh failed: %v", err)
			}

		case <-ctx.Done():
			logger.LogInfof("Bitmap snapshot refresh scheduler stopped")
			return
		}
	}
}

// OnDataChange 数据变更回调（用于实时同步）
func (m *BitmapManager) OnDataChange(ctx utils.LCOSContext, changeType string, lineKeys []LineKey) error {
	logger.CtxLogInfof(ctx, "Handling data change: type=%s, lines=%d", changeType, len(lineKeys))

	switch changeType {
	case "route_update", "route_create":
		// 路由数据更新，刷新相关快照
		return m.RefreshLineSnapshots(ctx, lineKeys)

	case "route_delete":
		// 路由数据删除，失效快照
		return m.InvalidateSnapshots(ctx, lineKeys)

	default:
		logger.CtxLogInfof(ctx, "Unknown data change type: %s", changeType)
		return nil
	}
}
