package fastpath

import (
	"context"
	"encoding/gob"
	"sync"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/bitmap"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/lib/localcache"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"github.com/RoaringBitmap/roaring"
)

var (
	// 确保只注册一次，避免重复注册
	gobRegistered sync.Once
)

func init() {
	// 使用 sync.Once 确保注册只执行一次
	gobRegistered.Do(func() {
		// 注册位图快照类型用于 gob 序列化
		gob.Register(&RouteWhitelistAgg{})
		gob.Register(&RouteBlacklistAgg{})
		gob.Register(&OriginAgg{})
		gob.Register(&DestAgg{})
		gob.Register(&LineSnapshot{})
		gob.Register(&BatchSnapshot{})
		gob.Register(roaring.New())
		gob.Register(map[uint64]*roaring.Bitmap{})
	})
}

type providerImpl struct {
	cache *localcache.Manager
	mutex sync.RWMutex // 保护并发访问
}

// NewProvider 创建 Snapshot Provider 实例
func NewProvider(cache *localcache.Manager) SnapshotProvider {
	return &providerImpl{cache: cache}
}

func (p *providerImpl) BatchGetLineSnapshots(ctx context.Context, keys []LineKey) (*BatchSnapshot, error) {
	bs := &BatchSnapshot{
		LineSnapshots: make(map[string]*LineSnapshot, len(keys)),
	}

	// Phase 1: 逐个获取（后续可优化为 MultiGet）
	for _, k := range keys {
		snap, _ := p.getSingleSnapshot(ctx, k)
		if snap != nil {
			// 使用线程安全的方法设置快照
			bs.SetSnapshot(k.LineID, snap)
		}
	}

	return bs, nil
}

func (p *providerImpl) BatchWarmup(ctx context.Context, keys []LineKey) error {
	// 预取逻辑，暂时简单实现
	_, err := p.BatchGetLineSnapshots(ctx, keys)
	return err
}

func (p *providerImpl) getSingleSnapshot(ctx context.Context, k LineKey) (*LineSnapshot, error) {
	// 1) 获取 route 聚合
	white := p.findWhitelistAgg(ctx, k)
	black := p.findBlacklistAgg(ctx, k)

	// 2) 获取 Origin/Dest 位图（Phase 2）
	origin := p.findOriginAgg(ctx, k)
	dest := p.findDestAgg(ctx, k)

	// 如果所有聚合都没有，返回 miss
	if white == nil && black == nil && origin == nil && dest == nil {
		return nil, nil
	}

	return &LineSnapshot{
		LineID:                k.LineID,
		CollectDeliverGroupID: k.CollectDeliverGroupID,
		Origin:                origin,
		Dest:                  dest,
		RouteWhite:            white,
		RouteBlack:            black,
	}, nil
}

func (p *providerImpl) findWhitelistAgg(ctx context.Context, k LineKey) *RouteWhitelistAgg {
	key := bitmap.GenerateAggCacheKey(k.LineID, k.CollectDeliverGroupID)

	lcosCtx, ok := ctx.(utils.LCOSContext)
	if !ok {
		return nil
	}

	res, err := localcache.NewLocalCacheQueryExecutor().Find(lcosCtx, constant.LineRouteV2WhitelistAggNamespace, key)
	if err != nil || res == nil {
		return nil
	}

	agg, ok := res.(*RouteWhitelistAgg)
	if !ok {
		return nil
	}

	return agg
}

func (p *providerImpl) findBlacklistAgg(ctx context.Context, k LineKey) *RouteBlacklistAgg {
	key := bitmap.GenerateAggCacheKey(k.LineID, k.CollectDeliverGroupID)

	lcosCtx, ok := ctx.(utils.LCOSContext)
	if !ok {
		return nil
	}

	res, err := localcache.NewLocalCacheQueryExecutor().Find(lcosCtx, constant.LineRouteV2BlacklistAggNamespace, key)
	if err != nil || res == nil {
		return nil
	}

	agg, ok := res.(*RouteBlacklistAgg)
	if !ok {
		return nil
	}

	return agg
}

// findOriginAgg 从 localcache 获取Origin聚合数据
func (p *providerImpl) findOriginAgg(ctx context.Context, k LineKey) *OriginAgg {
	key := bitmap.GenerateAggCacheKey(k.LineID, k.CollectDeliverGroupID)
	lcosCtx, ok := ctx.(utils.LCOSContext)
	if !ok {
		return nil
	}
	res, err := localcache.NewLocalCacheQueryExecutor().Find(lcosCtx, constant.LineOriginAggNamespace, key)
	if err != nil && err != localcache.ErrKeyNotFound {
		logger.CtxLogErrorf(lcosCtx, "Failed to get origin agg from cache for %s: %v", key, err)
		return nil
	}
	if res == nil {
		return nil
	}
	agg, ok := res.(*OriginAgg)
	if !ok {
		logger.CtxLogErrorf(lcosCtx, "Invalid type for origin agg from cache for %s", key)
		return nil
	}
	return agg
}

// findDestAgg 从 localcache 获取Dest聚合数据
func (p *providerImpl) findDestAgg(ctx context.Context, k LineKey) *DestAgg {
	key := bitmap.GenerateAggCacheKey(k.LineID, k.CollectDeliverGroupID)
	lcosCtx, ok := ctx.(utils.LCOSContext)
	if !ok {
		return nil
	}
	res, err := localcache.NewLocalCacheQueryExecutor().Find(lcosCtx, constant.LineDestAggNamespace, key)
	if err != nil && err != localcache.ErrKeyNotFound {
		logger.CtxLogErrorf(lcosCtx, "Failed to get dest agg from cache for %s: %v", key, err)
		return nil
	}
	if res == nil {
		return nil
	}
	agg, ok := res.(*DestAgg)
	if !ok {
		logger.CtxLogErrorf(lcosCtx, "Invalid type for dest agg from cache for %s", key)
		return nil
	}
	return agg
}
