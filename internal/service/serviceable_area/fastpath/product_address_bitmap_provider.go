package fastpath

import (
	"fmt"
	"sync"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
)

// ProductAddressBitmapProvider 真正的bitmap提供者
// 使用RoaringBitmap存储大量Product+Address组合，内存高效
type ProductAddressBitmapProvider interface {
	// IsServiceable 检查Product+Address组合是否支持配送
	// 直接查bitmap，未命中返回false，不会触发构建
	IsServiceable(ctx utils.LCOSContext, region string, key ProductAddressKey) bool

	// BatchIsServiceable 批量检查可配送性
	BatchIsServiceable(ctx utils.LCOSContext, region string, keys []ProductAddressKey) map[ProductAddressKey]bool

	// RebuildBitmap 重建bitmap（预计算使用）
	RebuildBitmap(ctx utils.LCOSContext, region string) error

	// GetBitmapStats 获取bitmap统计信息
	GetBitmapStats(region string) map[string]interface{}
}

// productAddressBitmapProviderImpl bitmap提供者实现
type productAddressBitmapProviderImpl struct {
	// 按地区存储bitmap
	bitmaps map[string]*ProductAddressBitmap

	// 索引提供者
	indexProvider ProductAddressIndexProvider

	// 数据源
	snapshotProvider SnapshotProvider

	// 并发控制
	mutex sync.RWMutex

	// 统计信息
	stats ProviderStats
}

// ProviderStats bitmap provider统计信息
type ProviderStats struct {
	QueryCount      int64     `json:"query_count"`
	CacheHits       int64     `json:"cache_hits"`    // bitmap命中
	CacheMisses     int64     `json:"cache_misses"`  // bitmap未命中
	RebuildCount    int64     `json:"rebuild_count"` // 重建次数
	LastRebuildTime time.Time `json:"last_rebuild_time"`
	mutex           sync.RWMutex
}

// NewProductAddressBitmapProvider 创建bitmap提供者
func NewProductAddressBitmapProvider(
	snapshotProvider SnapshotProvider,
) ProductAddressBitmapProvider {
	indexProvider := NewProductAddressIndexProvider()

	return &productAddressBitmapProviderImpl{
		bitmaps:          make(map[string]*ProductAddressBitmap),
		indexProvider:    indexProvider,
		snapshotProvider: snapshotProvider,
	}
}

// IsServiceable 检查是否支持配送
func (p *productAddressBitmapProviderImpl) IsServiceable(ctx utils.LCOSContext, region string, key ProductAddressKey) bool {
	start := time.Now()
	defer func() {
		duration := time.Since(start)
		SafeMetrics.GaugeSet("product_address_bitmap_query_duration_ms",
			float64(duration.Milliseconds()), map[string]string{"region": region})
	}()

	// 获取对应地区的bitmap
	bitmap := p.getBitmap(region)
	if bitmap == nil {
		logger.CtxLogInfof(ctx, "No bitmap found for region %s, returning false", region)
		p.updateStats(false)
		return false
	}

	// 直接查bitmap
	result := bitmap.IsServiceable(ctx, key)
	p.updateStats(result)

	return result
}

// BatchIsServiceable 批量检查可配送性
func (p *productAddressBitmapProviderImpl) BatchIsServiceable(ctx utils.LCOSContext, region string, keys []ProductAddressKey) map[ProductAddressKey]bool {
	start := time.Now()
	defer func() {
		duration := time.Since(start)
		SafeMetrics.GaugeSet("product_address_bitmap_batch_query_duration_ms",
			float64(duration.Milliseconds()), map[string]string{"region": region})
	}()

	result := make(map[ProductAddressKey]bool, len(keys))

	// 获取指定地区的bitmap
	bitmap := p.getBitmap(region)
	if bitmap == nil {
		// 该地区没有bitmap，全部返回false
		for _, key := range keys {
			result[key] = false
		}
		logger.CtxLogInfof(ctx, "No bitmap found for region %s, returning all false", region)
		return result
	}

	// 批量查询bitmap
	bitmapResults := bitmap.BatchIsServiceable(ctx, keys)
	for key, isServiceable := range bitmapResults {
		result[key] = isServiceable
		p.updateStats(isServiceable)
	}

	logger.CtxLogInfof(ctx, "Batch query %d keys in region %s", len(keys), region)
	return result
}

// RebuildBitmap 重建指定地区的bitmap
func (p *productAddressBitmapProviderImpl) RebuildBitmap(ctx utils.LCOSContext, region string) error {
	rebuildStart := time.Now()
	logger.CtxLogInfof(ctx, "Starting bitmap rebuild for region %s", region)

	// 1. 获取该地区所有Product+Address组合
	serviceableKeys, err := p.computeAllServiceableKeys(ctx, region)
	if err != nil {
		return fmt.Errorf("failed to compute serviceable keys for region %s: %w", region, err)
	}

	// 2. 创建或获取bitmap
	bitmap := p.getOrCreateBitmap(region)

	// 3. 重建bitmap
	bitmap.Rebuild(ctx, serviceableKeys)

	// 4. 更新统计
	p.stats.mutex.Lock()
	p.stats.RebuildCount++
	p.stats.LastRebuildTime = time.Now()
	p.stats.mutex.Unlock()

	duration := time.Since(rebuildStart)
	logger.CtxLogInfof(ctx, "Completed bitmap rebuild for region %s: %d serviceable keys, duration: %v",
		region, len(serviceableKeys), duration)

	// 上报监控指标（安全调用）
	SafeMetrics.GaugeSet("product_address_bitmap_rebuild_duration_seconds",
		duration.Seconds(), map[string]string{"region": region})
	SafeMetrics.GaugeSet("product_address_bitmap_serviceable_keys",
		float64(len(serviceableKeys)), map[string]string{"region": region})

	return nil
}

// computeAllServiceableKeys 计算所有支持配送的Product+Address组合
func (p *productAddressBitmapProviderImpl) computeAllServiceableKeys(ctx utils.LCOSContext, region string) ([]ProductAddressKey, error) {
	logger.CtxLogInfof(ctx, "Computing all serviceable keys for region %s", region)

	// 1. 获取所有Product的Lane信息
	productLanes, err := p.getAllProductLanes(ctx, region)
	if err != nil {
		return nil, fmt.Errorf("failed to get product lanes: %w", err)
	}

	// 2. 获取热门地址对
	addressPairs := p.getPopularAddressPairs(region)

	var serviceableKeys []ProductAddressKey

	// 3. 遍历所有Product+Address组合，检查是否支持配送
	for _, productLane := range productLanes {
		productId := int32(productLane.ProductId)

		for _, addressPair := range addressPairs {
			key := ProductAddressKey{
				ProductId:        productId,
				OriginLocationId: addressPair.OriginLocationId,
				DestLocationId:   addressPair.DestLocationId,
			}

			// 检查该组合是否支持配送
			if p.isProductAddressServiceable(ctx, key, productLane.LaneCodes) {
				serviceableKeys = append(serviceableKeys, key)
			}
		}
	}

	logger.CtxLogInfof(ctx, "Found %d serviceable keys out of %d total combinations for region %s",
		len(serviceableKeys), len(productLanes)*len(addressPairs), region)

	return serviceableKeys, nil
}

// isProductAddressServiceable 检查单个Product+Address组合是否支持配送
func (p *productAddressBitmapProviderImpl) isProductAddressServiceable(ctx utils.LCOSContext, key ProductAddressKey, laneCodes []string) bool {
	// 这里使用原有的逻辑：如果Product下任意一个Line同时支持pickup和deliver就认为支持
	for _, laneCode := range laneCodes {
		if p.isLaneServiceableForAddress(ctx, laneCode, key) {
			return true
		}
	}
	return false
}

// isLaneServiceableForAddress 检查Lane是否支持指定地址配送
func (p *productAddressBitmapProviderImpl) isLaneServiceableForAddress(ctx utils.LCOSContext, laneCode string, key ProductAddressKey) bool {
	// TODO: 这里需要实现具体的Lane+Address配送能力检查逻辑
	// 1. 通过LFS获取Lane信息
	// 2. 提取Line列表
	// 3. 检查每个Line是否同时支持pickup和deliver
	// 4. 如果有任意一个Line支持，返回true

	// 暂时简化实现，返回随机结果用于测试
	logger.CtxLogDebugf(ctx, "Checking lane %s serviceability for address %d->%d",
		laneCode, key.OriginLocationId, key.DestLocationId)

	// 简化逻辑：假设50%的组合支持配送
	hash := key.OriginLocationId + key.DestLocationId + uint64(key.ProductId)
	return hash%2 == 0
}

// getBitmap 获取指定地区的bitmap
func (p *productAddressBitmapProviderImpl) getBitmap(region string) *ProductAddressBitmap {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	return p.bitmaps[region]
}

// getOrCreateBitmap 获取或创建指定地区的bitmap
func (p *productAddressBitmapProviderImpl) getOrCreateBitmap(region string) *ProductAddressBitmap {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if bitmap, exists := p.bitmaps[region]; exists {
		return bitmap
	}

	bitmap := NewProductAddressBitmap(region, p.indexProvider)
	p.bitmaps[region] = bitmap
	return bitmap
}

// updateStats 更新统计信息
func (p *productAddressBitmapProviderImpl) updateStats(hit bool) {
	p.stats.mutex.Lock()
	defer p.stats.mutex.Unlock()

	p.stats.QueryCount++
	if hit {
		p.stats.CacheHits++
	} else {
		p.stats.CacheMisses++
	}
}

// GetBitmapStats 获取bitmap统计信息
func (p *productAddressBitmapProviderImpl) GetBitmapStats(region string) map[string]interface{} {
	bitmap := p.getBitmap(region)
	if bitmap == nil {
		return map[string]interface{}{
			"error": fmt.Sprintf("No bitmap found for region %s", region),
		}
	}

	bitmapInfo := bitmap.GetInfo()

	// 添加provider级别的统计
	p.stats.mutex.RLock()
	bitmapInfo["provider_query_count"] = p.stats.QueryCount
	bitmapInfo["provider_cache_hits"] = p.stats.CacheHits
	bitmapInfo["provider_cache_misses"] = p.stats.CacheMisses
	bitmapInfo["provider_hit_rate"] = float64(p.stats.CacheHits) / float64(p.stats.QueryCount)
	bitmapInfo["provider_rebuild_count"] = p.stats.RebuildCount
	bitmapInfo["provider_last_rebuild"] = p.stats.LastRebuildTime
	p.stats.mutex.RUnlock()

	return bitmapInfo
}

// getAllProductLanes 获取所有Product的Lane信息（复用原有逻辑）
func (p *productAddressBitmapProviderImpl) getAllProductLanes(ctx utils.LCOSContext, region string) ([]ProductLaneCodeList, error) {
	// 这里复用原来的逻辑
	// TODO: 从product_service.ListLaneCodesByRegion获取
	return []ProductLaneCodeList{
		{ProductId: 1001, LaneCodes: []string{"lane1", "lane2"}},
		{ProductId: 1002, LaneCodes: []string{"lane3", "lane4"}},
		{ProductId: 1003, LaneCodes: []string{"lane5", "lane6"}},
	}, nil
}

// getPopularAddressPairs 获取热门地址对（复用原有逻辑）
type AddressPair struct {
	OriginLocationId uint64
	DestLocationId   uint64
}

func (p *productAddressBitmapProviderImpl) getPopularAddressPairs(region string) []AddressPair {
	// 这里复用原来的逻辑
	switch region {
	case "SG":
		return []AddressPair{
			{OriginLocationId: 1001, DestLocationId: 2001},
			{OriginLocationId: 1002, DestLocationId: 2002},
			{OriginLocationId: 1003, DestLocationId: 2003},
		}
	default:
		return []AddressPair{
			{OriginLocationId: 9001, DestLocationId: 9002},
		}
	}
}

// ProductLaneCodeList 临时结构（应该从product_service导入）
type ProductLaneCodeList struct {
	ProductId int      `json:"product_id"`
	LaneCodes []string `json:"lane_codes"`
}
