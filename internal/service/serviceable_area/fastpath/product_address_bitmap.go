package fastpath

import (
	"fmt"
	"sync"
	"time"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	"github.com/RoaringBitmap/roaring"
)

// ProductAddressKey 产品+地址组合键
type ProductAddressKey struct {
	ProductId        int32
	OriginLocationId uint64
	DestLocationId   uint64
}

// String 生成缓存键字符串
func (k ProductAddressKey) String() string {
	return fmt.Sprintf("PRODUCT_ADDRESS:%d:%d:%d",
		k.ProductId, k.OriginLocationId, k.DestLocationId)
}

// ProductAddressBitmap 真正的产品地址bitmap
// 使用RoaringBitmap存储大量的(Product+Address)组合
type ProductAddressBitmap struct {
	// 核心bitmap，每个bit代表一个(Product+Address)组合是否支持配送
	bitmap *roaring.Bitmap

	// 索引提供者，负责将key映射到bitmap位置
	indexProvider ProductAddressIndexProvider

	// 元数据
	version   string    // bitmap版本
	buildTime time.Time // 构建时间
	region    string    // 所属地区

	// 并发控制
	mutex sync.RWMutex

	// 统计信息
	stats ProductAddressBitmapStats
}

// ProductAddressBitmapStats product address bitmap统计信息
type ProductAddressBitmapStats struct {
	TotalBits       uint64        `json:"total_bits"`       // 总bit数
	SetBits         uint64        `json:"set_bits"`         // 设置为1的bit数
	QueryCount      int64         `json:"query_count"`      // 查询次数
	HitCount        int64         `json:"hit_count"`        // 命中次数 (bit=1)
	MissCount       int64         `json:"miss_count"`       // 未命中次数 (bit=0)
	LastQueryTime   time.Time     `json:"last_query_time"`  // 最后查询时间
	BuildDuration   time.Duration `json:"build_duration"`   // 构建耗时
	CompressionSize int           `json:"compression_size"` // 压缩后大小(bytes)
}

// NewProductAddressBitmap 创建新的bitmap
func NewProductAddressBitmap(region string, indexProvider ProductAddressIndexProvider) *ProductAddressBitmap {
	return &ProductAddressBitmap{
		bitmap:        roaring.New(),
		indexProvider: indexProvider,
		region:        region,
		version:       fmt.Sprintf("v%d", time.Now().Unix()),
		buildTime:     time.Now(),
	}
}

// SetServiceable 设置某个Product+Address组合为可配送
func (b *ProductAddressBitmap) SetServiceable(ctx utils.LCOSContext, key ProductAddressKey) {
	b.mutex.Lock()
	defer b.mutex.Unlock()

	index := b.indexProvider.GetIndex(key)
	wasSet := b.bitmap.Contains(index)

	b.bitmap.Add(index)

	if !wasSet {
		b.stats.SetBits++
		logger.CtxLogDebugf(ctx, "Set serviceable: %v -> index %d", key, index)
	}

	b.updateTotalBits()
}

// IsServiceable 检查某个Product+Address组合是否可配送
func (b *ProductAddressBitmap) IsServiceable(ctx utils.LCOSContext, key ProductAddressKey) bool {
	b.mutex.RLock()
	defer b.mutex.RUnlock()

	index := b.indexProvider.GetIndex(key)
	result := b.bitmap.Contains(index)

	// 更新统计
	b.stats.QueryCount++
	b.stats.LastQueryTime = time.Now()
	if result {
		b.stats.HitCount++
	} else {
		b.stats.MissCount++
	}

	logger.CtxLogDebugf(ctx, "Query serviceable: %v -> index %d, result: %t", key, index, result)

	// 上报监控指标（安全调用，避免测试环境崩溃）
	SafeMetrics.CounterIncrWithContext(ctx, "product_address_bitmap_query_total",
		map[string]string{"region": b.region, "result": fmt.Sprintf("%t", result)})

	return result
}

// BatchSetServiceable 批量设置可配送
func (b *ProductAddressBitmap) BatchSetServiceable(ctx utils.LCOSContext, keys []ProductAddressKey) {
	b.mutex.Lock()
	defer b.mutex.Unlock()

	for _, key := range keys {
		index := b.indexProvider.GetIndex(key)
		if !b.bitmap.Contains(index) {
			b.bitmap.Add(index)
			b.stats.SetBits++
		}
	}

	b.updateTotalBits()
	logger.CtxLogInfof(ctx, "Batch set %d serviceable keys", len(keys))
}

// BatchIsServiceable 批量检查可配送性
func (b *ProductAddressBitmap) BatchIsServiceable(ctx utils.LCOSContext, keys []ProductAddressKey) map[ProductAddressKey]bool {
	b.mutex.RLock()
	defer b.mutex.RUnlock()

	result := make(map[ProductAddressKey]bool, len(keys))
	hitCount := 0

	for _, key := range keys {
		index := b.indexProvider.GetIndex(key)
		isServiceable := b.bitmap.Contains(index)
		result[key] = isServiceable

		if isServiceable {
			hitCount++
		}
	}

	// 更新统计
	b.stats.QueryCount += int64(len(keys))
	b.stats.HitCount += int64(hitCount)
	b.stats.MissCount += int64(len(keys) - hitCount)
	b.stats.LastQueryTime = time.Now()

	logger.CtxLogInfof(ctx, "Batch query %d keys: %d serviceable, %d not serviceable",
		len(keys), hitCount, len(keys)-hitCount)

	return result
}

// Clear 清空bitmap
func (b *ProductAddressBitmap) Clear(ctx utils.LCOSContext) {
	b.mutex.Lock()
	defer b.mutex.Unlock()

	b.bitmap.Clear()
	b.stats.SetBits = 0
	b.updateTotalBits()

	logger.CtxLogInfof(ctx, "Cleared bitmap for region %s", b.region)
}

// Rebuild 重建bitmap
func (b *ProductAddressBitmap) Rebuild(ctx utils.LCOSContext, serviceableKeys []ProductAddressKey) {
	buildStart := time.Now()

	b.mutex.Lock()
	defer b.mutex.Unlock()

	// 清空并重建
	b.bitmap.Clear()
	b.stats.SetBits = 0

	for _, key := range serviceableKeys {
		index := b.indexProvider.GetIndex(key)
		b.bitmap.Add(index)
		b.stats.SetBits++
	}

	b.updateTotalBits()
	b.version = fmt.Sprintf("v%d", time.Now().Unix())
	b.buildTime = time.Now()
	b.stats.BuildDuration = time.Since(buildStart)

	// 计算压缩大小
	serialized, _ := b.bitmap.ToBytes()
	b.stats.CompressionSize = len(serialized)

	logger.CtxLogInfof(ctx, "Rebuilt bitmap for region %s: %d keys, compression: %d bytes, duration: %v",
		b.region, len(serviceableKeys), b.stats.CompressionSize, b.stats.BuildDuration)

	// 上报监控指标（安全调用）
	SafeMetrics.GaugeSetWithContext(ctx, "product_address_bitmap_build_duration_ms",
		float64(b.stats.BuildDuration.Milliseconds()),
		map[string]string{"region": b.region})
	SafeMetrics.GaugeSetWithContext(ctx, "product_address_bitmap_compression_bytes",
		float64(b.stats.CompressionSize),
		map[string]string{"region": b.region})
}

// GetStats 获取统计信息
func (b *ProductAddressBitmap) GetStats() ProductAddressBitmapStats {
	b.mutex.RLock()
	defer b.mutex.RUnlock()
	return b.stats
}

// GetInfo 获取bitmap基本信息
func (b *ProductAddressBitmap) GetInfo() map[string]interface{} {
	b.mutex.RLock()
	defer b.mutex.RUnlock()

	return map[string]interface{}{
		"version":           b.version,
		"region":            b.region,
		"build_time":        b.buildTime,
		"total_bits":        b.stats.TotalBits,
		"set_bits":          b.stats.SetBits,
		"compression_ratio": float64(b.stats.SetBits) / float64(b.stats.TotalBits),
		"compression_size":  b.stats.CompressionSize,
		"query_count":       b.stats.QueryCount,
		"hit_rate":          float64(b.stats.HitCount) / float64(b.stats.QueryCount),
	}
}

// updateTotalBits 更新总bit数统计
func (b *ProductAddressBitmap) updateTotalBits() {
	if !b.bitmap.IsEmpty() {
		b.stats.TotalBits = uint64(b.bitmap.GetCardinality())
	}
}

// Clone 克隆bitmap（用于备份）
func (b *ProductAddressBitmap) Clone() *ProductAddressBitmap {
	b.mutex.RLock()
	defer b.mutex.RUnlock()

	cloned := &ProductAddressBitmap{
		bitmap:        b.bitmap.Clone(),
		indexProvider: b.indexProvider, // 共享索引提供者
		version:       b.version,
		buildTime:     b.buildTime,
		region:        b.region,
		stats:         b.stats, // 复制统计信息
	}

	return cloned
}

// Serialize 序列化bitmap到字节数组
func (b *ProductAddressBitmap) Serialize() ([]byte, error) {
	b.mutex.RLock()
	defer b.mutex.RUnlock()

	return b.bitmap.ToBytes()
}

// Deserialize 从字节数组反序列化bitmap
func (b *ProductAddressBitmap) Deserialize(data []byte) error {
	b.mutex.Lock()
	defer b.mutex.Unlock()

	newBitmap := roaring.New()
	if err := newBitmap.UnmarshalBinary(data); err != nil {
		return fmt.Errorf("failed to deserialize bitmap: %w", err)
	}

	b.bitmap = newBitmap
	b.updateTotalBits()

	return nil
}
