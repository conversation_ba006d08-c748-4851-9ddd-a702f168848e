package fastpath

import (
	"sync"

	"github.com/RoaringBitmap/roaring"
)

type DimensionKind uint8

const (
	DimLocationID DimensionKind = 1
	DimPostcode   DimensionKind = 2
	DimCepInt     DimensionKind = 3
)

// 单线能力的 4 个能力位
type CapabilityBits struct {
	CanPickup     bool
	CanCodPickup  bool
	CanDeliver    bool
	CanCodDeliver bool
}

// Route 白名单聚合：fromArea -> toArea 位图
type RouteWhitelistAgg struct {
	FromTo map[uint64]*roaring.Bitmap
	mutex  sync.RWMutex // 保证并发安全
}

// Route 黑名单聚合：全局标记 + fromArea -> toArea 位图（含占位符）
type RouteBlacklistAgg struct {
	GlobalBlocked bool
	FromTo        map[uint64]*roaring.Bitmap // toArea 位图，包含占位符 ToAreaPlaceholder
	mutex         sync.RWMutex               // 保证并发安全
}

// Origin/Dest 能力聚合（按不同维度分别存）
type OriginAgg struct {
	Kind       DimensionKind
	Allowed    *roaring.Bitmap // 所有允许的 key（locationId 或 规范化 postcode/cep）
	AllowedCod *roaring.Bitmap // COD 允许（可选）
	mutex      sync.RWMutex    // 保证并发安全
}

type DestAgg struct {
	Kind       DimensionKind
	Allowed    *roaring.Bitmap
	AllowedCod *roaring.Bitmap
	mutex      sync.RWMutex // 保证并发安全
}

// 最终对 (line, collectGroup) 的快照
type LineSnapshot struct {
	LineID                string
	CollectDeliverGroupID string
	Origin                *OriginAgg         // Phase 2: Origin 能力位图
	Dest                  *DestAgg           // Phase 2: Dest 能力位图
	RouteWhite            *RouteWhitelistAgg // Phase 1: Route 白名单
	RouteBlack            *RouteBlacklistAgg // Phase 1: Route 黑名单
}

// 整请求快照，便于批量读取与回填
type BatchSnapshot struct {
	LineSnapshots map[string]*LineSnapshot // key: lineId
	mutex         sync.RWMutex             // 保证并发安全
}

// LineKey 用于标识快照键
type LineKey struct {
	LineID                string
	CollectDeliverGroupID string
}

// String 方法用于调试和日志
func (k LineKey) String() string {
	return k.LineID + "|" + k.CollectDeliverGroupID
}

// 为聚合类型添加线程安全的访问方法

// GetFromTo 线程安全地获取FromTo映射
func (r *RouteWhitelistAgg) GetFromTo(fromAreaId uint64) *roaring.Bitmap {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	return r.FromTo[fromAreaId]
}

// SetFromTo 线程安全地设置FromTo映射
func (r *RouteWhitelistAgg) SetFromTo(fromAreaId uint64, bitmap *roaring.Bitmap) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	if r.FromTo == nil {
		r.FromTo = make(map[uint64]*roaring.Bitmap)
	}
	r.FromTo[fromAreaId] = bitmap
}

// GetFromTo 线程安全地获取FromTo映射
func (r *RouteBlacklistAgg) GetFromTo(fromAreaId uint64) *roaring.Bitmap {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	return r.FromTo[fromAreaId]
}

// SetFromTo 线程安全地设置FromTo映射
func (r *RouteBlacklistAgg) SetFromTo(fromAreaId uint64, bitmap *roaring.Bitmap) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	if r.FromTo == nil {
		r.FromTo = make(map[uint64]*roaring.Bitmap)
	}
	r.FromTo[fromAreaId] = bitmap
}

// GetAllowed 线程安全地获取Allowed位图
func (o *OriginAgg) GetAllowed() *roaring.Bitmap {
	o.mutex.RLock()
	defer o.mutex.RUnlock()
	return o.Allowed
}

// SetAllowed 线程安全地设置Allowed位图
func (o *OriginAgg) SetAllowed(bitmap *roaring.Bitmap) {
	o.mutex.Lock()
	defer o.mutex.Unlock()
	o.Allowed = bitmap
}

// GetAllowed 线程安全地获取Allowed位图
func (d *DestAgg) GetAllowed() *roaring.Bitmap {
	d.mutex.RLock()
	defer d.mutex.RUnlock()
	return d.Allowed
}

// SetAllowed 线程安全地设置Allowed位图
func (d *DestAgg) SetAllowed(bitmap *roaring.Bitmap) {
	d.mutex.Lock()
	defer d.mutex.Unlock()
	d.Allowed = bitmap
}

// GetSnapshot 线程安全地获取快照
func (b *BatchSnapshot) GetSnapshot(lineId string) *LineSnapshot {
	b.mutex.RLock()
	defer b.mutex.RUnlock()
	return b.LineSnapshots[lineId]
}

// SetSnapshot 线程安全地设置快照
func (b *BatchSnapshot) SetSnapshot(lineId string, snapshot *LineSnapshot) {
	b.mutex.Lock()
	defer b.mutex.Unlock()
	if b.LineSnapshots == nil {
		b.LineSnapshots = make(map[string]*LineSnapshot)
	}
	b.LineSnapshots[lineId] = snapshot
}
