package fastpath

import (
	"strconv"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	utils "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/common"
	pb "git.garena.com/shopee/bg-logistics/logistics/proto-center/v4/logistics-core-service/go"
)

// KeyNormalizer 负责地址键的规范化处理
type KeyNormalizer struct{}

// NewKeyNormalizer 创建键规范化器
func NewKeyNormalizer() *KeyNormalizer {
	return &KeyNormalizer{}
}

// NormalizeAddressKey 根据维度类型规范化地址键
func (kn *KeyNormalizer) NormalizeAddressKey(ctx utils.LCOSContext, addr *pb.ServiceableAddress, dimension DimensionKind) (uint64, error) {
	if addr == nil {
		return 0, nil
	}

	switch dimension {
	case DimLocationID:
		return kn.normalizeLocationKey(addr), nil
	case DimPostcode:
		return kn.normalizePostcodeKey(ctx, addr), nil
	case DimCepInt:
		return kn.normalizeCepKey(ctx, addr), nil
	default:
		return kn.normalizeLocationKey(addr), nil // 默认使用 LocationID
	}
}

// normalizeLocationKey 基于位置ID的键规范化（优先级：District > City > State）
func (kn *KeyNormalizer) normalizeLocationKey(addr *pb.ServiceableAddress) uint64 {
	// 优先级：District > City > State
	if addr.DistrictLocationId != nil && *addr.DistrictLocationId > 0 {
		return uint64(*addr.DistrictLocationId)
	}
	if addr.CityLocationId != nil && *addr.CityLocationId > 0 {
		return uint64(*addr.CityLocationId)
	}
	if addr.StateLocationId != nil && *addr.StateLocationId > 0 {
		return uint64(*addr.StateLocationId)
	}
	return 0
}

// normalizePostcodeKey 基于邮编的键规范化
func (kn *KeyNormalizer) normalizePostcodeKey(ctx utils.LCOSContext, addr *pb.ServiceableAddress) uint64 {
	postalCode := addr.GetPostalCode()
	if postalCode == "" {
		// 回退到 LocationID
		return kn.normalizeLocationKey(addr)
	}

	// 规范化邮编：移除非数字字符，转换为数字键
	normalized := kn.sanitizePostcode(postalCode)
	if normalized == "" {
		return kn.normalizeLocationKey(addr)
	}

	// 将邮编转换为 uint64 键
	key, err := strconv.ParseUint(normalized, 10, 64)
	if err != nil {
		logger.CtxLogInfof(ctx, "Failed to parse postcode %s: %v, falling back to locationId", normalized, err)
		return kn.normalizeLocationKey(addr)
	}

	return key
}

// normalizeCepKey 基于CEP范围的键规范化（巴西邮编格式）
func (kn *KeyNormalizer) normalizeCepKey(ctx utils.LCOSContext, addr *pb.ServiceableAddress) uint64 {
	cep := addr.GetPostalCode()
	if cep == "" {
		return kn.normalizeLocationKey(addr)
	}

	// CEP 格式通常是 XXXXX-XXX，取前5位作为范围键
	normalized := kn.sanitizePostcode(cep)
	if len(normalized) >= 5 {
		// 取前5位作为范围键
		rangeKey := normalized[:5]
		if key, err := strconv.ParseUint(rangeKey, 10, 64); err == nil {
			return key
		}
	}

	logger.CtxLogInfof(ctx, "Failed to normalize CEP %s, falling back to locationId", cep)
	return kn.normalizeLocationKey(addr)
}

// sanitizePostcode 清理邮编字符串，只保留数字
func (kn *KeyNormalizer) sanitizePostcode(postcode string) string {
	var result strings.Builder
	for _, r := range postcode {
		if r >= '0' && r <= '9' {
			result.WriteRune(r)
		}
	}
	return result.String()
}

// DetermineOptimalDimension 为给定地址确定最优的维度类型
func (kn *KeyNormalizer) DetermineOptimalDimension(addr *pb.ServiceableAddress) DimensionKind {
	if addr == nil {
		return DimLocationID
	}

	// 优先级策略：
	// 1. 如果有高精度的 DistrictLocationId，使用 LocationID 维度
	// 2. 如果有邮编且 locationId 精度不够，考虑使用 Postcode 维度
	// 3. 对于巴西等特定地区，可能优先使用 CEP 维度

	hasHighPrecisionLocation := addr.DistrictLocationId != nil && *addr.DistrictLocationId > 0
	hasPostcode := addr.GetPostalCode() != ""

	if hasHighPrecisionLocation {
		return DimLocationID
	}

	if hasPostcode {
		// 简单的地区判断：如果邮编看起来像巴西CEP格式，使用CEP维度
		if kn.looksLikeBrazilianCEP(addr.GetPostalCode()) {
			return DimCepInt
		}
		return DimPostcode
	}

	return DimLocationID // 默认维度
}

// looksLikeBrazilianCEP 简单判断是否像巴西CEP格式
func (kn *KeyNormalizer) looksLikeBrazilianCEP(postcode string) bool {
	cleaned := kn.sanitizePostcode(postcode)
	// 巴西 CEP 通常是 8 位数字
	return len(cleaned) == 8
}

// MultiDimensionKeys 为地址生成多维度键（用于兼容性或备选方案）
type MultiDimensionKeys struct {
	LocationKey uint64
	PostcodeKey uint64
	CepRangeKey uint64
	PrimaryKind DimensionKind
	PrimaryKey  uint64
}

// GenerateMultiDimensionKeys 生成地址的多维度键
func (kn *KeyNormalizer) GenerateMultiDimensionKeys(ctx utils.LCOSContext, addr *pb.ServiceableAddress) MultiDimensionKeys {
	keys := MultiDimensionKeys{}

	if addr == nil {
		return keys
	}

	// 生成各维度的键
	keys.LocationKey = kn.normalizeLocationKey(addr)
	keys.PostcodeKey = kn.normalizePostcodeKey(ctx, addr)
	keys.CepRangeKey = kn.normalizeCepKey(ctx, addr)

	// 确定主维度
	keys.PrimaryKind = kn.DetermineOptimalDimension(addr)

	// 设置主键
	switch keys.PrimaryKind {
	case DimLocationID:
		keys.PrimaryKey = keys.LocationKey
	case DimPostcode:
		keys.PrimaryKey = keys.PostcodeKey
	case DimCepInt:
		keys.PrimaryKey = keys.CepRangeKey
	default:
		keys.PrimaryKey = keys.LocationKey
	}

	return keys
}
