package fastpath

import (
	"crypto/md5"
	"encoding/binary"
	"fmt"
	"sync"
)

// ProductAddressIndexProvider 产品地址索引提供者
// 将(Product+Address)组合映射到bitmap中的索引位置
type ProductAddressIndexProvider interface {
	// GetIndex 获取ProductAddress组合在bitmap中的索引
	GetIndex(key ProductAddressKey) uint32

	// GetStats 获取索引统计信息
	GetStats() IndexStats
}

// IndexStats 索引统计信息
type IndexStats struct {
	QueryCount     int64 `json:"query_count"`     // 查询次数
	HashCollisions int64 `json:"hash_collisions"` // hash冲突数（预留）
}

// productAddressIndexProviderImpl 索引提供者实现
// 使用纯hash算法，不存储映射关系，节省内存
type productAddressIndexProviderImpl struct {
	stats IndexStats
	mutex sync.RWMutex
}

// NewProductAddressIndexProvider 创建索引提供者
func NewProductAddressIndexProvider() ProductAddressIndexProvider {
	return &productAddressIndexProviderImpl{}
}

// GetIndex 获取索引（纯hash算法，确定性映射）
func (p *productAddressIndexProviderImpl) GetIndex(key ProductAddressKey) uint32 {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	p.stats.QueryCount++
	return p.hashKey(key)
}

// hashKey 计算key的hash值作为索引
func (p *productAddressIndexProviderImpl) hashKey(key ProductAddressKey) uint32 {
	// 使用MD5确保确定性，相同的key总是得到相同的hash
	h := md5.New()
	h.Write([]byte(fmt.Sprintf("%d:%d:%d",
		key.ProductId, key.OriginLocationId, key.DestLocationId)))

	hash := h.Sum(nil)

	// 取前4个字节作为uint32
	return binary.BigEndian.Uint32(hash[:4])
}

// GetStats 获取统计信息
func (p *productAddressIndexProviderImpl) GetStats() IndexStats {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.stats
}
